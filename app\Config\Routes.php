<?php

namespace Config;
// Create a new instance of our RouteCollection class.
$routes = Services::routes();
/*
 * --------------------------------------------------------------------
 * Router Setup
 * --------------------------------------------------------------------
 */
$routes->setDefaultNamespace('App\Controllers');
$routes->setDefaultController('Home');
$routes->setDefaultMethod('index');
$routes->setTranslateURIDashes(false);
$routes->set404Override();
// The Auto Routing (Legacy) is very dangerous. It is easy to create vulnerable apps
// where controller filters or CSRF protection are bypassed.
// If you don't want to define all routes, please use the Auto Routing (Improved).
// Set `$autoRoutesImproved` to true in `app/Config/Feature.php` and set the following to true.
$routes->setAutoRoute(true);
/*
 * --------------------------------------------------------------------
 * Route Definitions
 * --------------------------------------------------------------------
 */
// We get a performance increase by specifying the default
// route since we don't have to scan directories.
$routes->get('/', 'Home::index');
$routes->get('/about', 'Home::about');
$routes->get('/contact', 'Home::contact');
$routes->get('/faq', 'Home::faq');

// Paper and Category routes
$routes->get('papers', 'Papers::index');
$routes->get('papers/view/(:num)', 'Papers::view/$1');
$routes->get('papers/latest', 'Papers::latest');
$routes->get('papers/category/(:num)', 'Papers::category/$1');
$routes->get('papers/download/(:num)', 'Papers::download/$1');
$routes->get('papers/preview/(:num)', 'Papers::preview/$1');
$routes->get('categories', 'Categories::index');
$routes->get('categories/view/(:num)', 'Categories::view/$1');

// Admin Auth routes
// Update all the lowercase HTTP methods to uppercase in the first part of the file
$routes->match(['GET', 'POST'], 'admin/login', 'Admin\Auth::login');
$routes->get('admin/logout', 'Admin\Auth::logout');
$routes->get('admin/google/callback', 'Admin\Auth::googleCallback');

// Admin protected routes
$routes->group('admin', ['filter' => 'admin'], function ($routes) {
    $routes->get('dashboard', 'Admin\Dashboard::index');

    // Papers management
    $routes->get('papers', 'Admin\Papers::index');
    $routes->match(['GET', 'POST'], 'papers/add', 'Admin\Papers::add');
    $routes->match(['GET', 'POST'], 'papers/edit/(:num)', 'Admin\Papers::edit/$1');
    $routes->get('papers/delete/(:num)', 'Admin\Papers::delete/$1');
    $routes->get('papers/view/(:num)', 'Admin\Papers::view/$1');
    $routes->get('papers/preview/(:num)', 'Admin\Papers::preview/$1');

    // Categories management
    $routes->get('categories', 'Admin\Categories::index');
    $routes->match(['GET', 'POST'], 'categories/create', 'Admin\Categories::create');
    $routes->match(['GET', 'POST'], 'categories/edit/(:num)', 'Admin\Categories::edit/$1');
    $routes->get('categories/delete/(:num)', 'Admin\Categories::delete/$1');

    // Users management
    $routes->get('users', 'Admin\Users::index');
    $routes->get('users/view/(:num)', 'Admin\Users::view/$1');
    $routes->match(['GET', 'POST'], 'users/edit/(:num)', 'Admin\Users::edit/$1');
    $routes->get('users/delete/(:num)', 'Admin\Users::delete/$1');
    $routes->get('users/ban/(:num)', 'Admin\Users::ban/$1');
    $routes->get('users/unban/(:num)', 'Admin\Users::unban/$1');

    // Orders management
    $routes->get('orders', 'Admin\Orders::index');
    $routes->get('orders/view/(:num)', 'Admin\Orders::view/$1');
    $routes->get('orders/update-status/(:num)/(:segment)', 'Admin\Orders::updateStatus/$1/$2');

    // Reports
    $routes->get('reports/sales', 'Admin\Reports::sales');
    $routes->get('reports/users', 'Admin\Reports::users');
    $routes->get('reports/papers', 'Admin\Reports::papers');
    $routes->get('reports/generate/(:segment)', 'Admin\Reports::generate/$1');

    // Settings
    $routes->get('settings', 'Admin\Settings::index');
    $routes->post('settings/update', 'Admin\Settings::update');

    // Admin Profile
    $routes->get('profile', 'Admin\Profile::index');
    $routes->match(['GET', 'POST'], 'profile/edit', 'Admin\Profile::edit');
});

// User authentication routes
$routes->match(['GET', 'POST'], 'auth/login', 'Auth::login');
$routes->match(['GET', 'POST'], 'auth/register', 'Auth::register');
$routes->get('auth/logout', 'Auth::logout');
$routes->get('auth/google/callback', 'Auth::googleCallback');
$routes->get('auth/profile', 'Auth::profile', ['filter' => 'auth']);
$routes->match(['GET', 'POST'], 'auth/profile/update', 'Auth::updateProfile', ['filter' => 'auth']);
$routes->match(['GET', 'POST'], 'auth/profile/change-password', 'Auth::changePassword', ['filter' => 'auth']);

// Password reset routes
$routes->get('auth/forgot-password', 'Auth::forgotPassword');
$routes->post('auth/forgot-password', 'Auth::processForgotPassword');
$routes->get('auth/reset-password/(:segment)', 'Auth::resetPassword/$1');
$routes->post('auth/reset-password', 'Auth::processResetPassword');

// API routes (for AJAX requests)

// Contact form submission
$routes->post('contact/submit', 'Home::submitContact');

// API Routes for frontend users
$routes->group('api', ['filter' => 'cors', 'namespace' => 'App\Controllers\Api'], function ($routes) {
    // Note: CSRF is disabled for API routes in Filters.php
    // Auth routes (no authentication required)
    $routes->post('auth/login', 'AuthController::login');
    $routes->post('auth/register', 'AuthController::register');

    // Public routes (no authentication required)
    $routes->get('papers', 'PaperController::index');
    $routes->get('papers/(:num)', 'PaperController::show/$1');
    $routes->get('papers/category/(:num)', 'PaperController::getByCategory/$1');
    $routes->get('papers/search', 'PaperController::search');
    $routes->get('categories', 'CategoryController::index');
    $routes->get('categories/(:num)', 'CategoryController::show/$1');

    // Protected routes (JWT authentication required)
    $routes->group('', ['filter' => 'jwt'], function ($routes) {
        // Auth routes
        $routes->get('auth/profile', 'AuthController::profile');
        $routes->post('auth/refresh-token', 'AuthController::refreshToken');

        // User routes
        $routes->post('user/update-profile', 'UserController::updateProfile');
        $routes->post('user/change-password', 'UserController::changePassword');
        $routes->get('user/orders', 'UserController::getOrders');
        $routes->get('user/orders/(:num)', 'UserController::getOrderDetails/$1');
    });
});

// Cart routes
$routes->get('cart', 'Cart::index');
$routes->post('cart/add', 'Cart::add');
$routes->get('cart/remove/(:num)', 'Cart::remove/$1');
$routes->get('cart/clear', 'Cart::clear');
$routes->get('cart/checkout', 'Cart::checkout');
$routes->post('cart/process', 'Cart::process');
$routes->get('cart/thankyou/(:num)', 'Cart::thankyou/$1');
$routes->post('cart/razorpay-webhook', 'Cart::razorpayWebhook');

// Profile routes
$routes->get('profile', 'Profile::index', ['filter' => 'auth']);
$routes->match(['GET', 'POST'], 'profile/edit', 'Profile::edit', ['filter' => 'auth']);
$routes->get('profile/orders', 'Profile::orders', ['filter' => 'auth']);
$routes->get('profile/viewOrder/(:num)', 'Profile::viewOrder/$1', ['filter' => 'auth']);
$routes->get('profile/downloads', 'Profile::downloads', ['filter' => 'auth']);
$routes->get('profile/download/(:num)', 'Profile::download/$1', ['filter' => 'auth']);
