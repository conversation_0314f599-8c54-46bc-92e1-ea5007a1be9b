<?= $this->extend('templates/admin_layout') ?>

<?= $this->section('content') ?>

<div class="card shadow-sm">
    <div class="card-header bg-transparent py-3 d-flex flex-row align-items-center justify-content-between">
        <h6 class="m-0 font-weight-bold text-primary">Paper Management</h6>
        <a href="<?= base_url('admin/papers/add') ?>" class="btn btn-primary btn-sm">
            <i class="fas fa-plus fa-sm"></i> Add New Paper
        </a>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover align-middle">
                <thead class="table-light">
                    <tr>
                        <th scope="col">ID</th>
                        <th scope="col">Title</th>
                        <th scope="col">Category</th>
                        <th scope="col">Price</th>
                        <th scope="col">Status</th>
                        <th scope="col">Created</th>
                        <th scope="col" class="text-end">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (!empty($papers)): ?>
                        <?php foreach ($papers as $index => $paper): ?>
                        <tr>
                            <td><?= $index + 1 ?></td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <?php if (!empty($paper['thumbnail'])): ?>
                                    <img src="<?= base_url('uploads/papers/' . $paper['thumbnail']) ?>" 
                                         alt="<?= esc($paper['title']) ?>" 
                                         class="me-2 rounded" 
                                         style="width: 40px; height: 40px; object-fit: cover;">
                                    <?php endif; ?>
                                    <div>
                                        <h6 class="mb-0"><?= esc($paper['title']) ?></h6>
                                        <?php if (!empty($paper['description'])): ?>
                                        <small class="text-muted"><?= substr(strip_tags($paper['description']), 0, 50) ?>...</small>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </td>
                            <td><?= esc($paper['category_name']) ?></td>
                            <td>$<?= number_format($paper['price'], 2) ?></td>
                            <td>
                                <span class="badge bg-<?= $paper['status'] === 'active' ? 'success' : 'secondary' ?>">
                                    <?= ucfirst($paper['status']) ?>
                                </span>
                            </td>
                            <td><?= date('M d, Y', strtotime($paper['created_at'])) ?></td>
                            <td>
                                <div class="d-flex justify-content-end gap-2">
                                    <a href="<?= base_url('admin/papers/view/' . $paper['id']) ?>" 
                                       class="btn btn-sm btn-outline-info" 
                                       title="View">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="<?= base_url('admin/papers/edit/' . $paper['id']) ?>" 
                                       class="btn btn-sm btn-outline-primary" 
                                       title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button type="button" 
                                            class="btn btn-sm btn-outline-danger delete-paper" 
                                            data-id="<?= $paper['id'] ?>" 
                                            title="Delete">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="7" class="text-center py-4">
                                <div class="text-muted">
                                    <i class="fas fa-file-alt fa-3x mb-3"></i>
                                    <p>No papers found</p>
                                </div>
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <?php if (isset($pager)): ?>
        <div class="d-flex justify-content-end mt-3">
            <?= $pager->links() ?>
        </div>
        <?php endif; ?>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete this paper? This action cannot be undone.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form action="<?= base_url('admin/papers/delete') ?>" method="post" class="d-inline">
                    <?= csrf_field() ?>
                    <input type="hidden" name="paper_id" id="paperIdToDelete">
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>

<style>
.table th {
    font-weight: 600;
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
}

.table td {
    vertical-align: middle;
}

.badge {
    font-weight: 500;
    padding: 0.5em 0.75em;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
}

.pagination {
    margin-bottom: 0;
}

.pagination .page-link {
    padding: 0.375rem 0.75rem;
    color: #2C3E50;
    background-color: #fff;
    border: 1px solid #dee2e6;
}

.pagination .page-item.active .page-link {
    background-color: #2C3E50;
    border-color: #2C3E50;
    color: #fff;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Delete paper confirmation
    const deleteButtons = document.querySelectorAll('.delete-paper');
    const paperIdInput = document.getElementById('paperIdToDelete');

    deleteButtons.forEach(button => {
        button.addEventListener('click', function() {
            const paperId = this.dataset.id;
            paperIdInput.value = paperId;
            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        });
    });
});
</script>

<?= $this->endSection() ?>