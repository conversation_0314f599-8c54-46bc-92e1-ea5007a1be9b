<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Models\UserModel;

class Profile extends BaseController
{
    protected $userModel;

    public function __construct()
    {
        $this->userModel = new UserModel();
    }

    public function index()
    {
        // Get the current admin user
        $userId = session()->get('id');
        $user = $this->userModel->find($userId);

        if (!$user) {
            return redirect()->to('/admin/auth/logout')->with('error', 'User not found.');
        }

        $data = [
            'title' => 'Admin Profile',
            'user' => $user
        ];

        return view('admin/profile/index', $data);
    }

    public function edit()
    {
        // Get the current admin user
        $userId = session()->get('id');
        $user = $this->userModel->find($userId);

        if (!$user) {
            return redirect()->to('/admin/auth/logout')->with('error', 'User not found.');
        }

        if ($this->request->getMethod() === 'post') {
            $rules = [
                'name' => 'required|min_length[3]|max_length[50]',
                'email' => 'required|valid_email'
            ];

            // Check if email is changed
            if ($user['email'] !== $this->request->getPost('email')) {
                $rules['email'] .= '|is_unique[users.email]';
            }

            // Check if password is being updated
            if ($this->request->getPost('password')) {
                $rules['password'] = 'required|min_length[6]';
                $rules['confirm_password'] = 'required|matches[password]';
            }

            if ($this->validate($rules)) {
                $updateData = [
                    'name' => $this->request->getPost('name'),
                    'email' => $this->request->getPost('email'),
                    'updated_at' => date('Y-m-d H:i:s')
                ];

                // Handle profile image upload
                $profileImage = $this->request->getFile('profile_image');
                if ($profileImage && $profileImage->isValid() && !$profileImage->hasMoved()) {
                    // Create directory if it doesn't exist
                    $uploadPath = FCPATH . 'uploads/profiles';
                    if (!is_dir($uploadPath)) {
                        mkdir($uploadPath, 0777, true);
                    }

                    // Generate a random name for the file
                    $newName = $profileImage->getRandomName();
                    
                    // Move the file to the upload directory
                    $profileImage->move($uploadPath, $newName);
                    
                    // Update the profile image path in the database
                    $updateData['profile_image'] = 'uploads/profiles/' . $newName;
                    
                    // Delete old image if it exists and is not a URL (Google profile image)
                    if (!empty($user['profile_image']) && !filter_var($user['profile_image'], FILTER_VALIDATE_URL)) {
                        $oldImagePath = FCPATH . $user['profile_image'];
                        if (file_exists($oldImagePath)) {
                            unlink($oldImagePath);
                        }
                    }
                }

                // Update password if provided
                if ($this->request->getPost('password')) {
                    $updateData['password'] = password_hash($this->request->getPost('password'), PASSWORD_DEFAULT);
                }

                $this->userModel->update($userId, $updateData);

                // Update session data
                session()->set('name', $updateData['name']);
                session()->set('email', $updateData['email']);
                
                // Get the updated user data to ensure we have the latest profile image
                $updatedUser = $this->userModel->find($userId);
                if ($updatedUser && isset($updatedUser['profile_image'])) {
                    session()->set('profile_image', $updatedUser['profile_image']);
                }

                return redirect()->to('/admin/profile')->with('success', 'Profile updated successfully.');
            } else {
                return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
            }
        }

        $data = [
            'title' => 'Edit Admin Profile',
            'user' => $user
        ];

        return view('admin/profile/edit', $data);
    }
}
