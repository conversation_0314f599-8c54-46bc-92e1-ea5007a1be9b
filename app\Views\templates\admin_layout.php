<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= isset($title) ? $title : 'Admin Dashboard - Sample Paper Store' ?></title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@400;600;700;800&display=swap" rel="stylesheet">
    <!-- <link href="<?= base_url('assets/css/admin-dashboard.css') ?>" rel="stylesheet"> -->
</head>

<body>
    <div class="d-flex w-100">
        <!-- Sidebar -->
        <div class="sidebar-wrapper">
            <div class="sidebar-brand">
                <img src="<?= base_url('public/assets/img/logo.svg') ?>" alt="Logo" class="sidebar-logo">
                <h1 class="sidebar-title">Admin Dashboard</h1>
            </div>

            <div class="sidebar-menu">
                <div class="menu-section">
                    <h2 class="menu-header">Main Navigation</h2>
                    <ul class="nav-links">
                        <li class="nav-item <?= current_url() == base_url('admin/dashboard') ? 'active' : '' ?>">
                            <a href="<?= base_url('admin/dashboard') ?>">
                                <i class="fas fa-tachometer-alt"></i>
                                <span>Dashboard</span>
                            </a>
                        </li>
                        <li class="nav-item <?= current_url() == base_url('admin/papers') ? 'active' : '' ?>">
                            <a href="<?= base_url('admin/papers') ?>">
                                <i class="fas fa-file-alt"></i>
                                <span>Papers</span>
                            </a>
                        </li>
                        <li class="nav-item <?= current_url() == base_url('admin/categories') ? 'active' : '' ?>">
                            <a href="<?= base_url('admin/categories') ?>">
                                <i class="fas fa-folder"></i>
                                <span>Categories</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <div class="menu-section">
                    <h2 class="menu-header">Management</h2>
                    <ul class="nav-links">
                        <li class="nav-item <?= current_url() == base_url('admin/users') ? 'active' : '' ?>">
                            <a href="<?= base_url('admin/users') ?>">
                                <i class="fas fa-users"></i>
                                <span>Users</span>
                            </a>
                        </li>
                        <li class="nav-item <?= current_url() == base_url('admin/orders') ? 'active' : '' ?>">
                            <a href="<?= base_url('admin/orders') ?>">
                                <i class="fas fa-shopping-cart"></i>
                                <span>Orders</span>
                            </a>
                        </li>
                        <li class="nav-item <?= current_url() == base_url('admin/reports/sales') ? 'active' : '' ?>">
                            <a href="<?= base_url('admin/reports/sales') ?>">
                                <i class="fas fa-chart-line"></i>
                                <span>Sales Reports</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <div class="menu-section mt-auto">
                    <ul class="nav-links">
                        <li class="nav-item">
                            <a href="<?= base_url('auth/logout') ?>" class="logout-link"
                                onclick="return confirm('Are you sure you want to logout?');">
                                <i class="fas fa-sign-out-alt"></i>
                                <span>Logout</span>
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <style>
            .sidebar-wrapper {
                width: 280px;
                height: 100vh;
                background: linear-gradient(180deg, #2C3E50 0%, #1a1a1a 100%);
                color: #fff;
                transition: all 0.3s ease;
            }

            .sidebar-brand {
                padding: 1.5rem;
                display: flex;
                align-items: center;
                border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            }

            .sidebar-logo {
                width: 40px;
                height: 40px;
                margin-right: 1rem;
            }

            .sidebar-title {
                font-size: 1.2rem;
                font-weight: 700;
                margin: 0;
                color: #fff;
            }

            .menu-section {
                padding: 1rem 0;
            }

            .menu-header {
                font-size: 0.75rem;
                text-transform: uppercase;
                letter-spacing: 0.1em;
                color: #6c757d;
                padding: 0 1.5rem;
                margin-bottom: 0.5rem;
            }

            .nav-links {
                list-style: none;
                padding: 0;
                margin: 0;
            }

            .nav-item {
                margin: 0.2rem 0;
            }

            .nav-item a {
                display: flex;
                align-items: center;
                padding: 0.75rem 1.5rem;
                color: rgba(255, 255, 255, 0.8);
                text-decoration: none;
                transition: all 0.3s ease;
            }

            .nav-item a:hover {
                background: rgba(255, 255, 255, 0.1);
                color: #fff;
            }

            .nav-item.active a {
                background: rgba(255, 255, 255, 0.15);
                color: #fff;
                border-left: 4px solid #3498db;
            }

            .nav-item i {
                width: 20px;
                margin-right: 10px;
                font-size: 1.1rem;
            }

            .logout-link {
                color: #ff6b6b !important;
            }

            .logout-link:hover {
                background: rgba(255, 59, 59, 0.1) !important;
            }

            .mt-auto {
                margin-top: auto !important;
            }
        </style>

        <!-- Page Content -->

        <div class="w-100">
            <nav class="navbar navbar-expand-lg custom-navbar">
                <div class="navbar-content ms-auto d-flex align-items-center">
                    <!-- Notifications Dropdown -->
                    <div class="dropdown me-3">
                        <a class="nav-link position-relative" href="#" id="notificationsDropdown" role="button"
                            data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-bell"></i>
                            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                                3
                            </span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end notification-dropdown" aria-labelledby="notificationsDropdown">
                            <li class="dropdown-header">Notifications</li>
                            <li><a class="dropdown-item" href="#">New order received</a></li>
                            <li><a class="dropdown-item" href="#">User report generated</a></li>
                            <li><a class="dropdown-item" href="#">System update completed</a></li>
                        </ul>
                    </div>

                    <!-- User Profile Dropdown -->
                    <div class="dropdown user-dropdown">
                        <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" id="userDropdown" role="button"
                            data-bs-toggle="dropdown" aria-expanded="false">
                            <div class="user-info me-2">
                                <span class="user-name"><?= session()->get('name') ?></span>
                                <span class="user-role">Administrator</span>
                            </div>
                            <?php if (!empty(session()->get('profile_image'))): ?>
                                <?php if (filter_var(session()->get('profile_image'), FILTER_VALIDATE_URL)): ?>
                                    <img class="rounded-circle user-avatar" width="40" src="<?= session()->get('profile_image') ?>" alt="Profile" referrerpolicy="no-referrer" onerror="this.onerror=null; this.src='<?= base_url('public/assets/img/default-avatar.svg') ?>';">
                                <?php else: ?>
                                    <img class="rounded-circle user-avatar" width="40" src="<?= base_url(session()->get('profile_image')) ?>" alt="Profile" onerror="this.onerror=null; this.src='<?= base_url('public/assets/img/default-avatar.svg') ?>';">
                                <?php endif; ?>
                            <?php else: ?>
                                <img class="rounded-circle user-avatar" width="40" src="<?= base_url('public/assets/img/default-avatar.svg') ?>" alt="Default Profile">
                            <?php endif; ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                            <li>
                                <a class="dropdown-item" href="<?= base_url('admin/profile') ?>">
                                    <i class="fas fa-user fa-sm fa-fw me-2"></i>Profile
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="<?= base_url('admin/settings') ?>">
                                    <i class="fas fa-cogs fa-sm fa-fw me-2"></i>Settings
                                </a>
                            </li>
                            <li>
                                <hr class="dropdown-divider">
                            </li>
                            <li>
                                <a class="dropdown-item text-danger" href="<?= base_url('admin/logout') ?>">
                                    <i class="fas fa-sign-out-alt fa-sm fa-fw me-2"></i>Logout
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>

            </nav>

            <style>
                .custom-navbar {
                    background: linear-gradient(80deg, #2C3E50 0%, #1a1a1a 100%);
                    color: rgba(255, 255, 255, 0.8);
                    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
                    border-bottom: none;
                }

                .user-dropdown .nav-link {
                    color: rgba(255, 255, 255, 0.8) !important;
                }

                .user-name {
                    color: #fff !important;
                }

                .user-role {
                    color: rgba(255, 255, 255, 0.6) !important;
                }

                .dropdown-menu {
                    background: #2C3E50;
                    border: 1px solid rgba(255, 255, 255, 0.1);
                }

                .dropdown-item {
                    color: rgba(255, 255, 255, 0.8) !important;
                }

                .dropdown-item:hover {
                    background: rgba(255, 255, 255, 0.1) !important;
                }

                .notification-dropdown .dropdown-header {
                    background: rgba(0, 0, 0, 0.2);
                    color: #fff;
                }

                .btn-toggle {
                    background: transparent;
                    border: none;
                    color: #2C3E50;
                    padding: 0.5rem;
                    border-radius: 0.375rem;
                    transition: all 0.3s ease;
                }

                .btn-toggle:hover {
                    background: rgba(0, 0, 0, 0.05);
                }

                .navbar-content {
                    gap: 1rem;
                }

                .user-dropdown .nav-link {
                    text-decoration: none;
                    color: #2C3E50;
                }

                .user-info {
                    text-align: right;
                }

                .user-name {
                    display: block;
                    font-weight: 600;
                    font-size: 0.9rem;
                    color: #2C3E50;
                }

                .user-role {
                    display: block;
                    font-size: 0.75rem;
                    color: #6c757d;
                }

                .user-avatar {
                    border: 2px solid #fff;
                    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                }

                .notification-dropdown {
                    background: #2C3E50 !important;
                    border-color: rgba(255, 255, 255, 0.1) !important;
                }

                .notification-dropdown .dropdown-item {
                    color: rgba(255, 255, 255, 0.8) !important;
                }

                .badge {
                    background-color: #3498db !important;
                }


                .dropdown-header {
                    background: #f8f9fa;
                    padding: 0.75rem 1rem;
                    font-weight: 600;
                    border-bottom: 1px solid #e9ecef;
                }

                .dropdown-item {
                    padding: 0.75rem 1rem;
                    transition: all 0.2s ease;
                }

                .dropdown-item:hover {
                    background: #f8f9fa;
                }

                .dropdown-item i {
                    color: #6c757d;
                }

                .text-danger i {
                    color: #dc3545;
                }
            </style>
            <div class="container-fluid p-4 bg-red">
                <?= $this->renderSection('content') ?>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="<?= base_url('public/assets/js/admin.js') ?>"></script>
</body>

</html>