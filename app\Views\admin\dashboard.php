<?= $this->extend('templates/admin_layout') ?>

<?= $this->section('content') ?>

<div class="container-fluid">
    <h1 class="h3 mb-4 text-gray-800 font-weight-bold">Dashboard Overview</h1>
    
    <div class="row ">
        <div class="col-xl-4 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2 hover-effect">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-sm font-weight-bold text-primary text-uppercase mb-2">
                                Total Users</div>
                            <div class="h3 mb-0 font-weight-bold text-gray-800"><?= $totalUsers ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-3x text-primary opacity-50"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-4 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2 hover-effect">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-sm font-weight-bold text-success text-uppercase mb-2">
                                Total Papers</div>
                            <div class="h3 mb-0 font-weight-bold text-gray-800"><?= $totalPapers ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-file-alt fa-3x text-success opacity-50"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-4 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2 hover-effect">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-sm font-weight-bold text-info text-uppercase mb-2">
                                Total Orders</div>
                            <div class="h3 mb-0 font-weight-bold text-gray-800"><?= $totalOrders ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-shopping-cart fa-3x text-info opacity-50"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-lg-6">
            <div class="card shadow mb-4 border-0">
                <div class="card-header py-3 bg-white">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-users mr-2"></i>Recent Users
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover" width="100%" cellspacing="0">
                            <thead class="bg-light">
                                <tr>
                                    <th class="font-weight-bold">Name</th>
                                    <th class="font-weight-bold">Email</th>
                                    <th class="font-weight-bold">Joined</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recentUsers as $user): ?>
                                <tr>
                                    <td class="align-middle"><?= $user['name'] ?></td>
                                    <td class="align-middle"><?= $user['email'] ?></td>
                                    <td class="align-middle"><span class="text-muted"><?= date('M d, Y', strtotime($user['created_at'])) ?></span></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-6">
            <div class="card shadow mb-4 border-0">
                <div class="card-header py-3 bg-white">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-shopping-cart mr-2"></i>Recent Orders
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover" width="100%" cellspacing="0">
                            <thead class="bg-light">
                                <tr>
                                    <th class="font-weight-bold">Order ID</th>
                                    <th class="font-weight-bold">Amount</th>
                                    <th class="font-weight-bold">Status</th>
                                    <th class="font-weight-bold">Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recentOrders as $order): ?>
                                <tr>
                                    <td class="align-middle">#<?= $order['id'] ?></td>
                                    <td class="align-middle font-weight-bold">$<?= number_format($order['total_amount'], 2) ?></td>
                                    <td class="align-middle">
                                        <?php if ($order['payment_status'] == 'completed'): ?>
                                            <span class="badge badge-soft-success px-3 py-2">Completed</span>
                                        <?php elseif ($order['payment_status'] == 'pending'): ?>
                                            <span class="badge badge-soft-warning px-3 py-2">Pending</span>
                                        <?php else: ?>
                                            <span class="badge badge-soft-danger px-3 py-2">Failed</span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="align-middle"><span class="text-muted"><?= date('M d, Y', strtotime($order['created_at'])) ?></span></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.hover-effect {
    transition: transform 0.2s ease;
}
.hover-effect:hover {
    transform: translateY(-5px);
}
.badge-soft-success {
    color: #0f5132;
    background-color: #d1e7dd;
}
.badge-soft-warning {
    color: #664d03;
    background-color: #fff3cd;
}
.badge-soft-danger {
    color: #842029;
    background-color: #f8d7da;
}
.opacity-50 {
    opacity: 0.5;
}
</style>

<?= $this->endSection() ?>