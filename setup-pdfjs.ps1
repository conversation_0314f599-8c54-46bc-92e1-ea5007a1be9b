# PowerShell script to setup PDF.js

# Create necessary directories
$pdfJsDir = "public/assets/pdfjs"
New-Item -ItemType Directory -Force -Path $pdfJsDir

# Download PDF.js
$version = "3.11.174"
$url = "https://github.com/mozilla/pdf.js/releases/download/v$version/pdfjs-$version-dist.zip"
$output = "pdfjs-dist.zip"

Write-Host "Downloading PDF.js v$version..."
Invoke-WebRequest -Uri $url -OutFile $output

# Extract the zip file
Write-Host "Extracting files..."
Expand-Archive -Path $output -DestinationPath "temp_pdfjs" -Force

# Copy required files
Write-Host "Copying files to $pdfJsDir..."
Copy-Item -Path "temp_pdfjs/web/*" -Destination "$pdfJsDir/web" -Recurse -Force
Copy-Item -Path "temp_pdfjs/build/*" -Destination "$pdfJsDir/build" -Recurse -Force

# Clean up
Write-Host "Cleaning up..."
Remove-Item -Path $output -Force
Remove-Item -Path "temp_pdfjs" -Recurse -Force

Write-Host "PDF.js setup complete!"