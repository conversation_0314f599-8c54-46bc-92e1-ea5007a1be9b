<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Models\PaperModel;
use App\Models\CategoryModel;

class Papers extends BaseController
{
    protected $paperModel;
    protected $categoryModel;
    
    public function __construct()
    {
        $this->paperModel = new PaperModel();
        $this->categoryModel = new CategoryModel();
    }
    
    public function index()
    {
        $data = [
            'title' => 'Manage Papers',
            'papers' => $this->paperModel->select('papers.*, categories.name as category_name')
                        ->join('categories', 'categories.id = papers.category_id')
                        ->orderBy('papers.created_at', 'DESC')
                        ->findAll()
        ];
        
        return view('admin/papers/index', $data);
    }
    
    public function add()
    {
        helper(['form']);
        
        if ($this->request->getMethod() === 'POST') {
            $rules = [
                'title' => 'required|min_length[3]|max_length[255]',
                'category_id' => 'required|numeric',
                'description' => 'required',
                'price' => 'required|numeric',
                'paper_file' => 'uploaded[paper_file]|max_size[paper_file,10240]|ext_in[paper_file,pdf]',
                'preview_file' => 'uploaded[preview_file]|max_size[preview_file,5120]',
            ];
            
            if ($this->validate($rules)) {
                $paperFile = $this->request->getFile('paper_file');
                $previewFile = $this->request->getFile('preview_file');
                
                if ($paperFile->isValid() && !$paperFile->hasMoved() && 
                    $previewFile->isValid() && !$previewFile->hasMoved()) {
                    
                    $paperName = $paperFile->getRandomName();
                    $previewName = $previewFile->getRandomName();
                    
                    $paperFile->move( 'uploads/papers', $paperName);
                    $previewFile->move(  'uploads/papers/previews', $previewName);
                    
                    $data = [
                        'title' => $this->request->getPost('title'),
                        'category_id' => $this->request->getPost('category_id'),
                        'description' => $this->request->getPost('description'),
                        'price' => $this->request->getPost('price'),
                        'file_path' => $paperName,
                        'preview_path' => $previewName,
                        'status' => $this->request->getPost('status') ?? 'active',
                        'year' => $this->request->getPost('year'),
                        'featured' => $this->request->getPost('featured') ? 1 : 0
                    ];
                    
                    $this->paperModel->insert($data);
                    return redirect()->to('/admin/papers')->with('success', 'Paper added successfully');
                }
            }
        }
        
        $data = [
            'title' => 'Add New Paper',
            'categories' => $this->categoryModel->findAll(),
            'validation' => $this->validator
        ];
        
        return view('admin/papers/add', $data);
    }
    
    public function edit($id = null)
    {
        helper(['form']);
        
        if (empty($id)) {
            return redirect()->to('/admin/papers')->with('error', 'Paper not found');
        }
        
        $paper = $this->paperModel->find($id);
        if (empty($paper)) {
            return redirect()->to('/admin/papers')->with('error', 'Paper not found');
        }
        
        if ($this->request->getMethod() === 'POST') {
            $rules = [
                'title' => 'required|min_length[3]|max_length[255]',
                'category_id' => 'required|numeric',
                'description' => 'required',
                'price' => 'required|numeric',
                'paper_file' => 'max_size[paper_file,10240]|ext_in[paper_file,pdf]',
                'preview_file' => 'max_size[preview_file,5120]',
            ];
            
            if ($this->validate($rules)) {
                $data = [
                    'title' => $this->request->getPost('title'),
                    'category_id' => $this->request->getPost('category_id'),
                    'description' => $this->request->getPost('description'),
                    'price' => $this->request->getPost('price'),
                    'status' => $this->request->getPost('status'),
                    'year' => $this->request->getPost('year'),
                    'featured' => $this->request->getPost('featured') ? 1 : 0
                ];
                
                $paperFile = $this->request->getFile('paper_file');
                if ($paperFile->isValid() && !$paperFile->hasMoved()) {
                    $paperName = $paperFile->getRandomName();
                    $paperFile->move( 'uploads/papers', $paperName);
                    $data['file_path'] = $paperName;
                    
                    // Delete old file
                    if ($paper['file_path'] && file_exists( 'uploads/papers/' . $paper['file_path'])) {
                        unlink( 'uploads/papers/' . $paper['file_path']);
                    }
                }
                
                $previewFile = $this->request->getFile('preview_file');
                if ($previewFile->isValid() && !$previewFile->hasMoved()) {
                    $previewName = $previewFile->getRandomName();
                    $previewFile->move( 'uploads/papers/previews', $previewName);
                    $data['preview_path'] = $previewName;
                    
                    // Delete old preview
                    if ($paper['preview_path'] && file_exists( 'uploads/papers/previews/' . $paper['preview_path'])) {
                        unlink( 'uploads/papers/previews/' . $paper['preview_path']);
                    }
                }
                
                $this->paperModel->update($id, $data);
                return redirect()->to('/admin/papers')->with('success', 'Paper updated successfully');
            }
        }
        
        $data = [
            'title' => 'Edit Paper',
            'paper' => $paper,
            'categories' => $this->categoryModel->findAll(),
            'validation' => $this->validator
        ];
        
        return view('admin/papers/edit', $data);
    }
    
    public function view($id = null)
    {
        if (empty($id)) {
            return redirect()->to('/admin/papers')->with('error', 'Paper not found');
        }
        
        $paper = $this->paperModel->select('papers.*, categories.name as category_name')
                                 ->join('categories', 'categories.id = papers.category_id')
                                 ->find($id);
        
        if (empty($paper)) {
            return redirect()->to('/admin/papers')->with('error', 'Paper not found');
        }
        
        $data = [
            'title' => 'View Paper',
            'paper' => $paper
        ];
        
        return view('admin/papers/view', $data);
    }
    
    public function delete($id = null)
    {
        if (empty($id)) {
            return redirect()->to('/admin/papers')->with('error', 'Paper not found');
        }
        
        $paper = $this->paperModel->find($id);
        if (empty($paper)) {
            return redirect()->to('/admin/papers')->with('error', 'Paper not found');
        }
        
        // Delete files
        if ($paper['file_path'] && file_exists( 'uploads/papers/' . $paper['file_path'])) {
            unlink( 'uploads/papers/' . $paper['file_path']);
        }
        if ($paper['preview_path'] && file_exists( 'uploads/papers/previews/' . $paper['preview_path'])) {
            unlink( 'uploads/papers/previews/' . $paper['preview_path']);
        }
        
        $this->paperModel->delete($id);
        return redirect()->to('/admin/papers')->with('success', 'Paper deleted successfully');
    }
}