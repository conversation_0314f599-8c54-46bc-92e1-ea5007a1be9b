<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Models\OrderModel;
use App\Models\OrderItemModel;
use App\Models\UserModel;
use App\Models\PaperModel;

class Reports extends BaseController
{
    protected $orderModel;
    protected $orderItemModel;
    protected $userModel;
    protected $paperModel;
    
    public function __construct()
    {
        $this->orderModel = new OrderModel();
        $this->orderItemModel = new OrderItemModel();
        $this->userModel = new UserModel();
        $this->paperModel = new PaperModel();
    }
    
    public function sales()
    {
        // Get sales data for the last 30 days
        $db = \Config\Database::connect();
        
        $query = $db->query("
            SELECT 
                DATE(created_at) as sale_date,
                SUM(total_amount) as daily_total,
                COUNT(*) as order_count
            FROM 
                orders
            WHERE 
                created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
                AND payment_status = 'completed'
            GROUP BY 
                DATE(created_at)
            ORDER BY 
                sale_date DESC
        ");
        
        $salesData = $query->getResultArray();
        
        // Get top selling papers
        $query = $db->query("
            SELECT 
                p.id,
                p.title,
                COUNT(oi.id) as order_count,
                SUM(oi.price) as total_revenue
            FROM 
                papers p
            JOIN 
                order_items oi ON p.id = oi.paper_id
            JOIN 
                orders o ON oi.order_id = o.id
            WHERE 
                o.payment_status = 'completed'
            GROUP BY 
                p.id
            ORDER BY 
                total_revenue DESC
            LIMIT 10
        ");
        
        $topPapers = $query->getResultArray();
        
        $data = [
            'title' => 'Sales Reports',
            'salesData' => $salesData,
            'topPapers' => $topPapers,
            'totalSales' => $this->orderModel->where('payment_status', 'completed')->selectSum('total_amount')->first()['total_amount'] ?? 0,
            'totalOrders' => $this->orderModel->where('payment_status', 'completed')->countAllResults(),
            'pendingOrders' => $this->orderModel->where('payment_status', 'pending')->countAllResults()
        ];
        
        return view('admin/reports/sales', $data);
    }
    
    public function users()
    {
        // Get user registration data for the last 30 days
        $db = \Config\Database::connect();
        
        $query = $db->query("
            SELECT 
                DATE(created_at) as reg_date,
                COUNT(*) as user_count
            FROM 
                users
            WHERE 
                created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
            GROUP BY 
                DATE(created_at)
            ORDER BY 
                reg_date DESC
        ");
        
        $userData = $query->getResultArray();
        
        // Get top purchasing users
        $query = $db->query("
            SELECT 
                u.id,
                u.name,
                u.email,
                COUNT(o.id) as order_count,
                SUM(o.total_amount) as total_spent
            FROM 
                users u
            JOIN 
                orders o ON u.id = o.user_id
            WHERE 
                o.payment_status = 'completed'
            GROUP BY 
                u.id
            ORDER BY 
                total_spent DESC
            LIMIT 10
        ");
        
        $topUsers = $query->getResultArray();
        
        $data = [
            'title' => 'User Reports',
            'userData' => $userData,
            'topUsers' => $topUsers,
            'totalUsers' => $this->userModel->countAll(),
            'activeUsers' => $this->userModel->where('status', 'active')->countAllResults(),
            'adminUsers' => $this->userModel->where('is_admin', 'admin')->countAllResults()
        ];
        
        return view('admin/reports/users', $data);
    }
    
    public function papers()
    {
        // Get paper data by category
        $db = \Config\Database::connect();
        
        $query = $db->query("
            SELECT 
                c.name as category_name,
                COUNT(p.id) as paper_count
            FROM 
                categories c
            LEFT JOIN 
                papers p ON c.id = p.category_id
            GROUP BY 
                c.id
            ORDER BY 
                paper_count DESC
        ");
        
        $categoryData = $query->getResultArray();
        
        // Get most viewed papers (assuming you have a views column or table)
        // For now, we'll just get the most recent papers
        $recentPapers = $this->paperModel->orderBy('created_at', 'DESC')->limit(10)->find();
        
        $data = [
            'title' => 'Paper Reports',
            'categoryData' => $categoryData,
            'recentPapers' => $recentPapers,
            'totalPapers' => $this->paperModel->countAll(),
            'activePapers' => $this->paperModel->where('status', 'active')->countAllResults()
        ];
        
        return view('admin/reports/papers', $data);
    }
    
    public function generate($type = 'sales')
    {
        // Generate CSV report based on type
        $filename = $type . '_report_' . date('Y-m-d') . '.csv';
        
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        
        $output = fopen('php://output', 'w');
        
        switch ($type) {
            case 'sales':
                // Headers
                fputcsv($output, ['Date', 'Order ID', 'Customer', 'Amount', 'Status']);
                
                // Data
                $orders = $this->orderModel->select('orders.*, users.name as user_name')
                                         ->join('users', 'users.id = orders.user_id')
                                         ->orderBy('orders.created_at', 'DESC')
                                         ->findAll();
                
                foreach ($orders as $order) {
                    fputcsv($output, [
                        date('Y-m-d', strtotime($order['created_at'])),
                        $order['id'],
                        $order['user_name'],
                        $order['total_amount'],
                        $order['payment_status']
                    ]);
                }
                break;
                
            case 'users':
                // Headers
                fputcsv($output, ['ID', 'Name', 'Email', 'Status', 'Registration Date']);
                
                // Data
                $users = $this->userModel->orderBy('created_at', 'DESC')->findAll();
                
                foreach ($users as $user) {
                    fputcsv($output, [
                        $user['id'],
                        $user['name'],
                        $user['email'],
                        $user['status'],
                        date('Y-m-d', strtotime($user['created_at']))
                    ]);
                }
                break;
                
            case 'papers':
                // Headers
                fputcsv($output, ['ID', 'Title', 'Category', 'Price', 'Status', 'Created Date']);
                
                // Data
                $papers = $this->paperModel->select('papers.*, categories.name as category_name')
                                         ->join('categories', 'categories.id = papers.category_id')
                                         ->orderBy('papers.created_at', 'DESC')
                                         ->findAll();
                
                foreach ($papers as $paper) {
                    fputcsv($output, [
                        $paper['id'],
                        $paper['title'],
                        $paper['category_name'],
                        $paper['price'],
                        $paper['status'],
                        date('Y-m-d', strtotime($paper['created_at']))
                    ]);
                }
                break;
                
            default:
                return redirect()->to('/admin/reports/sales')->with('error', 'Invalid report type.');
        }
        
        fclose($output);
        exit;
    }
}
