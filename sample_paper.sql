-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Apr 09, 2025 at 10:51 AM
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `sample_paper`
--

-- --------------------------------------------------------

--
-- Table structure for table `categories`
--

CREATE TABLE `categories` (
  `id` int(11) UNSIGNED NOT NULL,
  `name` varchar(100) NOT NULL,
  `preview_image` text NOT NULL,
  `description` text DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

--
-- Dumping data for table `categories`
--

INSERT INTO `categories` (`id`, `name`, `preview_image`, `description`, `created_at`, `updated_at`) VALUES
(2, 'SSC', 'uploads/categories/1744007460_db7fcf978f70c879b1e2.jpg', 'SSC Exams Papers', '2025-04-07 06:31:00', '2025-04-07 06:59:24'),
(3, 'UPSC', 'uploads/categories/1744009199_afeb4c5a575016013018.jpg', 'UPSC Exam Paper', '2025-04-07 06:59:59', '2025-04-07 06:59:59'),
(4, 'RRB', 'uploads/categories/1744009246_ad6cf1ff95bd83919096.jpg', 'RRB Exam Paper', '2025-04-07 07:00:46', '2025-04-07 07:00:46'),
(5, 'AIBE', 'uploads/categories/1744019188_e6465b0c05bd64c13444.jpg', 'AIBE Exam Paper', '2025-04-07 07:01:48', '2025-04-07 09:46:28');

-- --------------------------------------------------------

--
-- Table structure for table `migrations`
--

CREATE TABLE `migrations` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `version` varchar(255) NOT NULL,
  `class` varchar(255) NOT NULL,
  `group` varchar(255) NOT NULL,
  `namespace` varchar(255) NOT NULL,
  `time` int(11) NOT NULL,
  `batch` int(11) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

--
-- Dumping data for table `migrations`
--

INSERT INTO `migrations` (`id`, `version`, `class`, `group`, `namespace`, `time`, `batch`) VALUES
(1, '2023-06-01-000001', 'App\\Database\\Migrations\\CreateUsersTable', 'default', 'App', 1743574242, 1),
(2, '2023-06-01-000002', 'App\\Database\\Migrations\\CreateCategoriesTable', 'default', 'App', 1743574242, 1),
(3, '2023-06-01-000003', 'App\\Database\\Migrations\\CreatePapersTable', 'default', 'App', 1743574242, 1),
(4, '2023-06-01-000004', 'App\\Database\\Migrations\\CreateOrdersTable', 'default', 'App', 1743574242, 1),
(5, '2023-06-01-000005', 'App\\Database\\Migrations\\CreateOrderItemsTable', 'default', 'App', 1743574243, 1),
(6, '2023-10-01-000001', 'App\\Database\\Migrations\\CreateUsersTable', 'default', 'App', 1743574272, 2),
(7, '2024_05_01_000001', 'App\\Database\\Migrations\\AddGoogleAuthToUsers', 'default', 'App', 1744177351, 3),
(8, '2025_04_09_000001', 'App\\Database\\Migrations\\CreatePasswordResetsTable', 'default', 'App', 1744185805, 4),
(9, '2025_04_09_000002', 'App\\Database\\Migrations\\CreatePasswordResetAttemptsTable', 'default', 'App', 1744186447, 5);

-- --------------------------------------------------------

--
-- Table structure for table `orders`
--

CREATE TABLE `orders` (
  `id` int(11) UNSIGNED NOT NULL,
  `user_id` int(11) UNSIGNED NOT NULL,
  `total_amount` decimal(10,2) NOT NULL,
  `payment_status` enum('pending','completed','failed') NOT NULL DEFAULT 'pending',
  `payment_method` varchar(50) DEFAULT NULL,
  `transaction_id` varchar(100) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

--
-- Dumping data for table `orders`
--

INSERT INTO `orders` (`id`, `user_id`, `total_amount`, `payment_status`, `payment_method`, `transaction_id`, `created_at`, `updated_at`) VALUES
(1, 1, 10.00, 'failed', 'paypal', 'TXN1744095697285', '2025-04-08 07:01:37', '2025-04-08 07:06:58'),
(2, 1, 10.00, 'completed', 'paypal', 'TXN1744095876914', '2025-04-08 07:04:36', '2025-04-08 07:05:55'),
(3, 1, 10.00, 'completed', 'paypal', 'TXN1744096836955', '2025-04-08 07:20:36', '2025-04-08 07:20:36'),
(4, 1, 10.00, 'completed', 'bank_transfer', 'TXN1744099169977', '2025-04-08 07:59:29', '2025-04-08 08:03:31'),
(5, 1, 10.00, 'completed', 'razorpay', 'pay_QGV0dZj0ukAfN7', '2025-04-08 08:35:11', '2025-04-08 08:35:11'),
(6, 1, 10.00, 'completed', 'razorpay', 'pay_QGV3DPmCrkK7hF', '2025-04-08 08:37:36', '2025-04-08 08:37:36');

-- --------------------------------------------------------

--
-- Table structure for table `order_items`
--

CREATE TABLE `order_items` (
  `id` int(11) UNSIGNED NOT NULL,
  `order_id` int(11) UNSIGNED NOT NULL,
  `paper_id` int(11) UNSIGNED NOT NULL,
  `price` decimal(10,2) NOT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

--
-- Dumping data for table `order_items`
--

INSERT INTO `order_items` (`id`, `order_id`, `paper_id`, `price`, `created_at`, `updated_at`) VALUES
(1, 3, 5, 10.00, '2025-04-08 07:20:36', '2025-04-08 07:20:36'),
(2, 4, 8, 10.00, '2025-04-08 07:59:29', '2025-04-08 07:59:29'),
(3, 5, 10, 10.00, '2025-04-08 08:35:11', '2025-04-08 08:35:11'),
(4, 6, 6, 10.00, '2025-04-08 08:37:36', '2025-04-08 08:37:36');

-- --------------------------------------------------------

--
-- Table structure for table `papers`
--

CREATE TABLE `papers` (
  `id` int(11) UNSIGNED NOT NULL,
  `title` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `category_id` int(11) UNSIGNED NOT NULL,
  `price` decimal(10,2) NOT NULL,
  `file_path` varchar(255) NOT NULL,
  `preview_path` varchar(255) DEFAULT NULL,
  `status` enum('active','inactive') NOT NULL DEFAULT 'active',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

--
-- Dumping data for table `papers`
--

INSERT INTO `papers` (`id`, `title`, `description`, `category_id`, `price`, `file_path`, `preview_path`, `status`, `created_at`, `updated_at`) VALUES
(2, 'SSC Sample Paper - 1 ( 2025 )', 'SSC Sample Paper - 1 ( 2025 )', 2, 100.00, '1744015356_84a27f5a49aac43fc7ec.pdf', '1744015356_56470ff4b4b6dfe7b4d2.jpg', 'active', '2025-04-07 06:50:13', '2025-04-07 11:13:23'),
(3, 'AIBE Sample Paper -2 ( 2025 )', 'AIBE Sample Paper -2 ( 2025 )', 5, 10.00, '1744012796_f379aae94adfbb7665d2.pdf', '1744012796_5577f04001c2823c7371.jpg', 'active', '2025-04-07 07:04:31', '2025-04-07 11:16:17'),
(4, 'SSC Sample Paper - 2 ( 2025 )', 'SSC Sample Paper - 2 ( 2025 )', 2, 10.00, '1744024439_77e214d88b224c1f8a01.pdf', '1744024439_7521ec3790806a947869.jpg', 'active', '2025-04-07 11:13:59', '2025-04-07 11:13:59'),
(5, 'SSC Sample Paper - 3 ( 2025 )', 'SSC Sample Paper - 3 ( 2025 )', 2, 10.00, '1744024485_f95910d9842cf5bb6a05.pdf', '1744024485_aef50532a696fd9745e3.jpg', 'active', '2025-04-07 11:14:45', '2025-04-07 11:14:45'),
(6, 'AIBE Sample Paper -1 ( 2025 )', 'AIBE Sample Paper -1 ( 2025 )', 5, 10.00, '1744024548_abb7463fa490942e9e05.pdf', '1744024548_dd9d560ff6c2cce29466.jpg', 'active', '2025-04-07 11:15:48', '2025-04-07 11:15:48'),
(7, 'UPSC Sample Paper -1 ( 2024 )', 'UPSC Sample Paper -1 ( 2024 )', 3, 10.00, '1744025871_23e54e07663a1cd282fb.pdf', '1744025871_06fc5159bc2d8b1bc8de.jpg', 'active', '2025-04-07 11:37:51', '2025-04-07 11:37:51'),
(8, 'UPSC Sample Paper - 2 ( 2024 )', 'UPSC Sample Paper -2 ( 2024 )', 3, 10.00, '1744025894_f088f02f6624dc830aae.pdf', '1744025894_6e31cb10ace18151562f.jpg', 'active', '2025-04-07 11:38:14', '2025-04-07 11:38:14'),
(9, 'RRB Sample Paper -1 ( 2024 )', 'RRB Sample Paper -1 ( 2024 )', 4, 10.00, '1744026032_5f2a09722a342ed1dc51.pdf', '1744026032_d2ff558630ed1a1e924a.jpg', 'active', '2025-04-07 11:40:32', '2025-04-07 11:40:32'),
(10, 'RRB Sample Paper -2 ( 2024 )', 'RRB Sample Paper -2 ( 2024 )', 4, 10.00, '1744026057_cdf50950a7b867b69d9d.pdf', '1744026057_89bddda4d57a0bd232c9.jpg', 'active', '2025-04-07 11:40:57', '2025-04-07 11:40:57');

-- --------------------------------------------------------

--
-- Table structure for table `password_resets`
--

CREATE TABLE `password_resets` (
  `id` int(11) UNSIGNED NOT NULL,
  `email` varchar(100) NOT NULL,
  `token` varchar(255) NOT NULL,
  `created_at` datetime DEFAULT NULL,
  `expires_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

--
-- Dumping data for table `password_resets`
--

INSERT INTO `password_resets` (`id`, `email`, `token`, `created_at`, `expires_at`) VALUES
(5, '<EMAIL>', 'c289f1d1aeda17b154761b0cbd264397e7226cb3212a8640300f110454252b0b', '2025-04-09 08:43:12', '2025-04-09 09:43:12');

-- --------------------------------------------------------

--
-- Table structure for table `password_reset_attempts`
--

CREATE TABLE `password_reset_attempts` (
  `id` int(11) UNSIGNED NOT NULL,
  `ip_address` varchar(45) NOT NULL,
  `email` varchar(100) NOT NULL,
  `created_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

--
-- Dumping data for table `password_reset_attempts`
--

INSERT INTO `password_reset_attempts` (`id`, `ip_address`, `email`, `created_at`) VALUES
(1, '::1', '<EMAIL>', '2025-04-09 08:25:45'),
(2, '::1', '<EMAIL>', '2025-04-09 08:39:44'),
(3, '::1', '<EMAIL>', '2025-04-09 08:43:12');

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id` int(11) UNSIGNED NOT NULL,
  `name` varchar(100) NOT NULL,
  `email` varchar(100) NOT NULL,
  `password` varchar(255) NOT NULL,
  `google_id` varchar(255) DEFAULT NULL,
  `oauth_provider` varchar(50) DEFAULT NULL,
  `profile_image` varchar(255) DEFAULT NULL,
  `is_admin` varchar(10) NOT NULL DEFAULT '0',
  `status` enum('active','banned') NOT NULL DEFAULT 'active',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`id`, `name`, `email`, `password`, `google_id`, `oauth_provider`, `profile_image`, `is_admin`, `status`, `created_at`, `updated_at`) VALUES
(1, 'Admin User', '<EMAIL>', '$2y$10$C8X3UOBujM.7vWZcmRk91uY.rThDgaCp4C22Cu/kyGfp15Uadd7jS', NULL, NULL, NULL, 'admin', 'active', '2025-04-02 06:41:09', '2025-04-02 06:41:09'),
(3, 'Krish Yadav', '<EMAIL>', '$2y$10$XQzTgigLlG9wazibqTcECudjQ1Yjp0hQSI4F6cC8TyZ9Cgc4Qq0DS', '101448415027388344605', 'google', 'https://lh3.googleusercontent.com/a/ACg8ocIjZfqPK2anmEdeM6sesdjp7JMliwbOnwDr8PNG3f3QVCYvVLc=s96-c', 'user', 'active', '2025-04-07 08:39:12', '2025-04-09 08:40:01');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `categories`
--
ALTER TABLE `categories`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `migrations`
--
ALTER TABLE `migrations`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `orders`
--
ALTER TABLE `orders`
  ADD PRIMARY KEY (`id`),
  ADD KEY `orders_user_id_foreign` (`user_id`);

--
-- Indexes for table `order_items`
--
ALTER TABLE `order_items`
  ADD PRIMARY KEY (`id`),
  ADD KEY `order_items_order_id_foreign` (`order_id`),
  ADD KEY `order_items_paper_id_foreign` (`paper_id`);

--
-- Indexes for table `papers`
--
ALTER TABLE `papers`
  ADD PRIMARY KEY (`id`),
  ADD KEY `papers_category_id_foreign` (`category_id`);

--
-- Indexes for table `password_resets`
--
ALTER TABLE `password_resets`
  ADD PRIMARY KEY (`id`),
  ADD KEY `email` (`email`),
  ADD KEY `token` (`token`);

--
-- Indexes for table `password_reset_attempts`
--
ALTER TABLE `password_reset_attempts`
  ADD PRIMARY KEY (`id`),
  ADD KEY `ip_address` (`ip_address`),
  ADD KEY `email` (`email`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `email` (`email`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `categories`
--
ALTER TABLE `categories`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `migrations`
--
ALTER TABLE `migrations`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- AUTO_INCREMENT for table `orders`
--
ALTER TABLE `orders`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `order_items`
--
ALTER TABLE `order_items`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `papers`
--
ALTER TABLE `papers`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- AUTO_INCREMENT for table `password_resets`
--
ALTER TABLE `password_resets`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `password_reset_attempts`
--
ALTER TABLE `password_reset_attempts`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `orders`
--
ALTER TABLE `orders`
  ADD CONSTRAINT `orders_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Constraints for table `order_items`
--
ALTER TABLE `order_items`
  ADD CONSTRAINT `order_items_order_id_foreign` FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `order_items_paper_id_foreign` FOREIGN KEY (`paper_id`) REFERENCES `papers` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Constraints for table `papers`
--
ALTER TABLE `papers`
  ADD CONSTRAINT `papers_category_id_foreign` FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
