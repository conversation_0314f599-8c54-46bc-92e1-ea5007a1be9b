<?php

namespace App\Controllers;

use App\Models\UserModel;
use App\Libraries\GoogleClient;

class Auth extends BaseController
{
    protected $userModel;
    protected $googleClient;

    public function __construct()
    {
        $this->userModel = new UserModel();
        $this->googleClient = new GoogleClient();
    }
    public function login()
    {
        // Check if already logged in
        if (session()->get('isLoggedIn') && !session()->get('isAdmin')) {
            return redirect()->to('/');
        } else if (session()->get('isLoggedIn') && session()->get('isAdmin')) {
            return redirect()->to('admin/dashboard');
        }

        $data = [
            'title' => 'User Login - Sample Paper Store',
            'googleLoginUrl' => $this->googleClient->getUserLoginUrl()
        ];

        if ($this->request->getMethod() === 'POST') {
            $rules = [
                'email' => 'required|valid_email',
                'password' => 'required|min_length[6]'
            ];

            if ($this->validate($rules)) {
                // Using the model from constructor

                $email = $this->request->getPost('email');
                $password = $this->request->getPost('password');

                $user = $this->userModel->getUserByEmail($email);

                if ($user && password_verify($password, $user['password'])) {
                    // Check if user is active
                    if ($user['status'] !== 'active') {
                        return redirect()->back()->withInput()->with('error', 'Your account is not active. Please contact support.');
                    }

                    // Set session data
                    $sessionData = [
                        'id' => $user['id'],
                        'name' => $user['name'],
                        'email' => $user['email'],
                        'isAdmin' => ($user['is_admin'] === 'admin') ? true : false,
                        'isLoggedIn' => true,
                        'profile_image' => $user['profile_image'] ?? null
                    ];

                    session()->set($sessionData);

                    // Redirect based on role
                    if ($user['is_admin'] === 'admin') {
                        return redirect()->to('admin/dashboard');
                    } else {
                        return redirect()->to('/');
                    }
                } else {
                    return redirect()->back()->withInput()->with('error', 'Invalid email or password');
                }
            } else {
                return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
            }
        }

        return view('auth/login', $data);
    }

    public function register()
    {


        // Check if already logged in
        if (session()->get('isLoggedIn')) {
            return redirect()->to('/');
        }

        $data = [
            'title' => 'Register - Sample Paper Store'
        ];

        if ($this->request->getMethod() === 'POST') {
            $rules = [
                'name' => 'required|min_length[3]|max_length[50]',
                'email' => 'required|valid_email|is_unique[users.email]',
                'password' => 'required|min_length[6]',
                'confirm_password' => 'required|matches[password]'
            ];

            if ($this->validate($rules)) {
                // Using the model from constructor

                $userData = [
                    'name' => $this->request->getPost('name'),
                    'email' => $this->request->getPost('email'),
                    'password' => password_hash($this->request->getPost('password'), PASSWORD_DEFAULT),
                    'is_admin' => 'user',
                    'status' => 'active',
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ];

                try {
                    // Debug: Log the data being inserted
                    log_message('debug', 'Attempting to register user: ' . json_encode($userData));

                    $result = $this->userModel->insert($userData);

                    if ($result === false) {
                        log_message('error', 'User registration failed: ' . print_r($this->userModel->errors(), true));
                        return redirect()->back()->withInput()->with('error', 'Registration failed: ' . implode(', ', $this->userModel->errors()));
                    }

                    return redirect()->to('auth/login')->with('success', 'Registration successful! You can now login.');
                } catch (\Exception $e) {
                    log_message('error', 'Exception during registration: ' . $e->getMessage());
                    return redirect()->back()->withInput()->with('error', 'Database error: ' . $e->getMessage());
                }
            } else {
                return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
            }
        }

        return view('auth/register', $data);
    }

    public function logout()
    {
        session()->destroy();
        return redirect()->to('/');
    }

    /**
     * Handle Google login callback
     */
    public function googleCallback()
    {
        $code = $this->request->getGet('code');

        if (!$code) {
            return redirect()->to('auth/login')->with('error', 'Authorization failed');
        }

        try {
            // Get user info from Google
            $userInfo = $this->googleClient->getUserInfo($code);

            // Debug log the user info
            log_message('debug', 'Google user info: ' . json_encode((array)$userInfo));

            // Check if user exists with this Google ID
            $user = $this->userModel->getUserByGoogleId($userInfo->id);

            if (!$user) {
                // Check if user exists with this email
                $user = $this->userModel->getUserByEmail($userInfo->email);

                if ($user) {
                    // Update existing user with Google ID
                    $this->userModel->update($user['id'], [
                        'google_id' => $userInfo->id,
                        'oauth_provider' => 'google',
                        'profile_image' => $userInfo->picture ?? null,
                        'updated_at' => date('Y-m-d H:i:s')
                    ]);
                } else {
                    // Create new user
                    $userData = [
                        'name' => $userInfo->name,
                        'email' => $userInfo->email,
                        'password' => password_hash(random_bytes(16), PASSWORD_DEFAULT), // Random password
                        'is_admin' => 'user',
                        'status' => 'active',
                        'google_id' => $userInfo->id,
                        'oauth_provider' => 'google',
                        'profile_image' => $userInfo->picture ?? null,
                        'created_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s')
                    ];

                    $this->userModel->insert($userData);
                    $user = $this->userModel->getUserByGoogleId($userInfo->id);
                }
            }

            // Set session data
            $sessionData = [
                'id' => $user['id'],
                'name' => $user['name'],
                'email' => $user['email'],
                'isAdmin' => ($user['is_admin'] === 'admin') ? true : false,
                'isLoggedIn' => true,
                'profile_image' => $user['profile_image'] ?? null
            ];

            session()->set($sessionData);

            // Redirect based on role
            if ($user['is_admin'] === 'admin') {
                return redirect()->to('admin/dashboard');
            } else {
                return redirect()->to('/');
            }
        } catch (\Exception $e) {
            log_message('error', 'Google authentication error: ' . $e->getMessage());
            log_message('error', 'Stack trace: ' . $e->getTraceAsString());
            return redirect()->to('auth/login')->with('error', 'Authentication failed: ' . $e->getMessage());
        } catch (\Error $e) {
            log_message('error', 'Google authentication error: ' . $e->getMessage());
            log_message('error', 'Stack trace: ' . $e->getTraceAsString());
            return redirect()->to('auth/login')->with('error', 'Authentication error: ' . $e->getMessage());
        }
    }

    // Other methods like forgotPassword, resetPassword can be implemented as needed
}
