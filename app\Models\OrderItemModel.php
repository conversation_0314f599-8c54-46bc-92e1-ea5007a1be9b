<?php

namespace App\Models;

use CodeIgniter\Model;

class OrderItemModel extends Model
{
    protected $table = 'order_items';
    protected $primaryKey = 'id';
    protected $allowedFields = ['order_id', 'paper_id', 'price'];
    protected $useTimestamps = true;
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $returnType = 'array';

    /**
     * Get items for a specific order
     */
    public function getOrderItems($orderId)
    {
        return $this->select('order_items.*, papers.title as paper_title, papers.preview_path')
                    ->join('papers', 'papers.id = order_items.paper_id')
                    ->where('order_items.order_id', $orderId)
                    ->findAll();
    }

    /**
     * Calculate total for an order
     */
    public function calculateOrderTotal($orderId)
    {
        $result = $this->selectSum('price')
                       ->where('order_id', $orderId)
                       ->first();

        return $result['price'] ?? 0;
    }
}
