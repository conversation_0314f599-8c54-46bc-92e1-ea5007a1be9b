<?= $this->extend('templates/main_layout') ?>

<?= $this->section('content') ?>
<div class="container py-5">
    <div class="row">
        <div class="col-lg-3">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">My Account</h5>
                </div>
                <div class="list-group list-group-flush">
                    <a href="<?= base_url('profile') ?>" class="list-group-item list-group-item-action">
                        <i class="fas fa-user me-2"></i>Profile
                    </a>
                    <a href="<?= base_url('profile/orders') ?>" class="list-group-item list-group-item-action">
                        <i class="fas fa-shopping-bag me-2"></i>Orders
                    </a>
                    <a href="<?= base_url('profile/downloads') ?>" class="list-group-item list-group-item-action">
                        <i class="fas fa-download me-2"></i>Downloads
                    </a>
                    <a href="<?= base_url('auth/logout') ?>" class="list-group-item list-group-item-action text-danger">
                        <i class="fas fa-sign-out-alt me-2"></i>Logout
                    </a>
                </div>
            </div>
        </div>

        <div class="col-lg-9">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Edit Profile</h5>
                </div>
                <div class="card-body">
                    <?php if (session()->getFlashdata('success')): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <?= session()->getFlashdata('success') ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <?php if (session()->getFlashdata('errors')): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <h6>Please fix the following errors:</h6>
                            <ul>
                                <?php foreach (session()->getFlashdata('errors') as $error): ?>
                                    <li><?= esc($error) ?></li>
                                <?php endforeach; ?>
                            </ul>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <form action="<?= base_url('profile/edit') ?>" method="post" enctype="multipart/form-data">
                        <?= csrf_field() ?>
                        <div class="mb-4 text-center">
                            <div class="mb-3">
                                <?php if (!empty($user['profile_image'])): ?>
                                    <?php if (filter_var($user['profile_image'], FILTER_VALIDATE_URL)): ?>
                                        <img src="<?= $user['profile_image'] ?>" alt="Profile" class="rounded-circle img-thumbnail" style="width: 150px; height: 150px; object-fit: cover;" referrerpolicy="no-referrer" onerror="this.onerror=null; this.src='<?= base_url('assets/images/default-avatar.svg') ?>'; this.style.width='150px'; this.style.height='150px';">
                                    <?php else: ?>
                                        <img src="<?= base_url($user['profile_image']) ?>" alt="Profile" class="rounded-circle img-thumbnail" style="width: 150px; height: 150px; object-fit: cover;" onerror="this.onerror=null; this.src='<?= base_url('assets/images/default-avatar.svg') ?>'; this.style.width='150px'; this.style.height='150px';">
                                    <?php endif; ?>
                                <?php else: ?>
                                    <img src="<?= base_url('assets/images/default-avatar.svg') ?>" alt="Profile" class="rounded-circle img-thumbnail" style="width: 150px; height: 150px;">
                                <?php endif; ?>
                            </div>
                            <div class="mb-3">
                                <label for="profile_image" class="form-label">Profile Image</label>
                                <input type="file" class="form-control" id="profile_image" name="profile_image" accept="image/*">
                                <div class="form-text">Upload a new profile image (optional). Maximum size: 2MB. Supported formats: JPG, PNG, GIF.</div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="name" class="form-label">Name</label>
                            <input type="text" class="form-control" id="name" name="name" value="<?= esc($user['name']) ?>" required>
                        </div>
                        <div class="mb-3">
                            <label for="email" class="form-label">Email</label>
                            <input type="email" class="form-control" id="email" name="email" value="<?= esc($user['email']) ?>" required>
                        </div>

                        <hr class="my-4">

                        <h5>Change Password</h5>
                        <p class="text-muted small">Leave blank if you don't want to change your password.</p>

                        <div class="mb-3">
                            <label for="password" class="form-label">New Password</label>
                            <input type="password" class="form-control" id="password" name="password">
                            <div class="form-text">Password must be at least 6 characters long.</div>
                        </div>
                        <div class="mb-3">
                            <label for="confirm_password" class="form-label">Confirm New Password</label>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password">
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="<?= base_url('profile') ?>" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Back to Profile
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Save Changes
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>