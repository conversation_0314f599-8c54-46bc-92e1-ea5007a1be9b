<?php

/**
 * Simple script to test the API directly
 */

// API endpoint
$url = 'http://localhost/sample-paper/api/auth/login';

// Test credentials
$data = [
    'email' => '<EMAIL>',
    'password' => 'vinay@1234'
];

// Initialize cURL session
$ch = curl_init($url);

// Set cURL options
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Accept: application/json',
    'X-Requested-With: XMLHttpRequest'
]);

// Enable verbose output for debugging
curl_setopt($ch, CURLOPT_VERBOSE, true);
$verbose = fopen('php://temp', 'w+');
curl_setopt($ch, CURLOPT_STDERR, $verbose);

// Execute cURL session
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

// Check for errors
if (curl_errno($ch)) {
    echo "cURL Error: " . curl_error($ch) . "\n";
} else {
    echo "HTTP Status Code: " . $httpCode . "\n\n";
    echo "Raw Response:\n" . $response . "\n\n";

    // Try to parse JSON response
    $jsonResponse = json_decode($response, true);
    if ($jsonResponse !== null) {
        echo "Parsed JSON Response:\n";
        print_r($jsonResponse);
    } else {
        echo "Response is not valid JSON\n";
    }
}

// Get verbose information
rewind($verbose);
$verboseLog = stream_get_contents($verbose);
echo "\nVerbose information:\n", $verboseLog, "\n";

// Close cURL session
curl_close($ch);
