<?= $this->extend('templates/main_layout') ?>

<?= $this->section('content') ?>
<div class="container py-5">
    <div class="row">
        <div class="col-lg-3">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">My Account</h5>
                </div>
                <div class="list-group list-group-flush">
                    <a href="<?= base_url('profile') ?>" class="list-group-item list-group-item-action active">
                        <i class="fas fa-user me-2"></i>Profile
                    </a>
                    <a href="<?= base_url('profile/orders') ?>" class="list-group-item list-group-item-action">
                        <i class="fas fa-shopping-bag me-2"></i>Orders
                    </a>
                    <a href="<?= base_url('profile/downloads') ?>" class="list-group-item list-group-item-action">
                        <i class="fas fa-download me-2"></i>Downloads
                    </a>
                    <a href="<?= base_url('auth/logout') ?>" class="list-group-item list-group-item-action text-danger">
                        <i class="fas fa-sign-out-alt me-2"></i>Logout
                    </a>
                </div>
            </div>
        </div>

        <div class="col-lg-9">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">My Profile</h5>
                    <a href="<?= base_url('profile/edit') ?>" class="btn btn-light btn-sm">
                        <i class="fas fa-edit me-1"></i>Edit Profile
                    </a>
                </div>
                <div class="card-body">
                    <?php if (session()->getFlashdata('success')): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <?= session()->getFlashdata('success') ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <div class="row">
                        <div class="col-md-3 text-center mb-4">
                            <?php if (!empty($user['profile_image'])): ?>
                                <?php if (filter_var($user['profile_image'], FILTER_VALIDATE_URL)): ?>
                                    <img src="<?= $user['profile_image'] ?>" alt="Profile" class="rounded-circle img-thumbnail" style="width: 150px; height: 150px; object-fit: cover;" referrerpolicy="no-referrer" onerror="this.onerror=null; this.src='<?= base_url('assets/images/default-avatar.svg') ?>'; this.style.width='150px'; this.style.height='150px';">
                                <?php else: ?>
                                    <img src="<?= base_url($user['profile_image']) ?>" alt="Profile" class="rounded-circle img-thumbnail" style="width: 150px; height: 150px; object-fit: cover;" onerror="this.onerror=null; this.src='<?= base_url('assets/images/default-avatar.svg') ?>'; this.style.width='150px'; this.style.height='150px';">
                                <?php endif; ?>
                            <?php else: ?>
                                <img src="<?= base_url('assets/images/default-avatar.svg') ?>" alt="Profile" class="rounded-circle img-thumbnail" style="width: 150px; height: 150px;">
                            <?php endif; ?>
                        </div>
                        <div class="col-md-9">
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>Name:</strong> <?= esc($user['name']) ?></p>
                                    <p><strong>Email:</strong> <?= esc($user['email']) ?></p>
                                    <p><strong>Account Type:</strong> <?= $user['is_admin'] === 'admin' ? 'Administrator' : 'Customer' ?></p>
                                    <p><strong>Status:</strong> <?= ucfirst($user['status']) ?></p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>Member Since:</strong> <?= date('F j, Y', strtotime($user['created_at'])) ?></p>
                                    <p><strong>Last Updated:</strong> <?= date('F j, Y', strtotime($user['updated_at'])) ?></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Account Security</h5>
                </div>
                <div class="card-body">
                    <p>To update your password, please visit the <a href="<?= base_url('profile/edit') ?>">Edit Profile</a> page.</p>

                    <div class="alert alert-info">
                        <h6><i class="fas fa-shield-alt me-2"></i>Security Tips</h6>
                        <ul class="mb-0">
                            <li>Use a strong, unique password</li>
                            <li>Never share your login credentials</li>
                            <li>Log out when using shared computers</li>
                            <li>Update your password regularly</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>
