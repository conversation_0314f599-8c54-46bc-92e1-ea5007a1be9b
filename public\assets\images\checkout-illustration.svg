<?xml version="1.0" encoding="UTF-8"?>
<svg width="400px" height="200px" viewBox="0 0 400 200" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>Checkout Illustration</title>
    <defs>
        <linearGradient x1="0%" y1="0%" x2="100%" y2="100%" id="linearGradient-1">
            <stop stop-color="#4361EE" offset="0%"></stop>
            <stop stop-color="#3A56D4" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="checkout-illustration">
            <rect id="background" fill="#F8F9FA" x="0" y="0" width="400" height="200" rx="10"></rect>
            <g id="secure-payment" transform="translate(100.000000, 30.000000)">
                <rect id="card" fill="url(#linearGradient-1)" x="0" y="0" width="200" height="120" rx="10"></rect>
                <rect id="chip" fill="#FFD700" x="20" y="30" width="30" height="20" rx="3"></rect>
                <rect id="number" fill="#FFFFFF" opacity="0.8" x="20" y="70" width="160" height="10" rx="5"></rect>
                <rect id="name" fill="#FFFFFF" opacity="0.8" x="20" y="90" width="80" height="10" rx="5"></rect>
                <rect id="expiry" fill="#FFFFFF" opacity="0.8" x="140" y="90" width="40" height="10" rx="5"></rect>
                <circle id="lock" fill="#FFFFFF" cx="180" cy="30" r="15"></circle>
                <path d="M180,25 L180,25 C177.790861,25 176,26.790861 176,29 L176,35 C176,37.209139 177.790861,39 180,39 L180,39 C182.209139,39 184,37.209139 184,35 L184,29 C184,26.790861 182.209139,25 180,25 Z" id="lock-body" fill="#4361EE"></path>
                <rect id="lock-hole" fill="#4361EE" x="178" y="20" width="4" height="8" rx="2"></rect>
            </g>
            <g id="shield" transform="translate(50.000000, 80.000000)">
                <path d="M25,0 L25,0 C38.8071187,0 50,11.1928813 50,25 L50,25 C50,38.8071187 38.8071187,50 25,50 L25,50 C11.1928813,50 0,38.8071187 0,25 L0,25 C0,11.1928813 11.1928813,0 25,0 Z" id="shield-background" fill="#4361EE" opacity="0.2"></path>
                <path d="M25,10 L36,15 C36,25 32,35 25,40 C18,35 14,25 14,15 L25,10 Z" id="shield-shape" stroke="#4361EE" stroke-width="2" fill="#FFFFFF"></path>
                <polyline id="check" stroke="#4361EE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" points="19 25 23 29 31 21"></polyline>
            </g>
            <g id="cart" transform="translate(300.000000, 80.000000)">
                <path d="M25,0 L25,0 C38.8071187,0 50,11.1928813 50,25 L50,25 C50,38.8071187 38.8071187,50 25,50 L25,50 C11.1928813,50 0,38.8071187 0,25 L0,25 C0,11.1928813 11.1928813,0 25,0 Z" id="cart-background" fill="#4361EE" opacity="0.2"></path>
                <path d="M15,20 L35,20 L32,30 L18,30 L15,20 Z M20,35 C20,36.1045695 19.1045695,37 18,37 C16.8954305,37 16,36.1045695 16,35 C16,33.8954305 16.8954305,33 18,33 C19.1045695,33 20,33.8954305 20,35 Z M34,35 C34,36.1045695 33.1045695,37 32,37 C30.8954305,37 30,36.1045695 30,35 C30,33.8954305 30.8954305,33 32,33 C33.1045695,33 34,33.8954305 34,35 Z" id="cart-shape" stroke="#4361EE" stroke-width="2" fill="#FFFFFF"></path>
                <path d="M20,20 L20,15 C20,12.790861 22.2385763,11 25,11 C27.7614237,11 30,12.790861 30,15 L30,20" id="handle" stroke="#4361EE" stroke-width="2"></path>
            </g>
        </g>
    </g>
</svg>
