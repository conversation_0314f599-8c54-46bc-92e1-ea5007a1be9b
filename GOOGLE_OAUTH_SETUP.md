# Google OAuth Integration Setup

This document provides instructions for setting up Google OAuth integration for the Sample Paper Store application.

## Prerequisites

1. A Google Cloud Platform account
2. Access to the Google Cloud Console

## Steps to Set Up Google OAuth

### 1. Create a Project in Google Cloud Console

1. Go to the [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Note down the Project ID

### 2. Enable the Google OAuth API

1. In the Google Cloud Console, navigate to "APIs & Services" > "Library"
2. Search for "Google OAuth2 API" and enable it
3. Also enable the "Google+ API" if you need access to user profile information

### 3. Configure OAuth Consent Screen

1. Go to "APIs & Services" > "OAuth consent screen"
2. Select the appropriate user type (External or Internal)
3. Fill in the required information:
   - App name: "Sample Paper Store"
   - User support email
   - Developer contact information
4. Add the following scopes:
   - `email`
   - `profile`
   - `openid`
5. Add your domain to the authorized domains list
6. Save and continue

### 4. Create OAuth Client ID

1. Go to "APIs & Services" > "Credentials"
2. Click "Create Credentials" and select "OAuth client ID"
3. Select "Web application" as the application type
4. Add a name for your client (e.g., "Sample Paper Store Web Client")
5. Add the following Authorized JavaScript origins:
   - `http://localhost`
   - `https://yourdomain.com` (your production domain)
6. Add the following Authorized redirect URIs:
   - `http://localhost/auth/google/callback` (for user login)
   - `http://localhost/admin/google/callback` (for admin login)
   - `https://yourdomain.com/auth/google/callback` (production)
   - `https://yourdomain.com/admin/google/callback` (production)
7. Click "Create"
8. Note down the Client ID and Client Secret

### 5. Configure the Application

1. Open the `app/Config/Google.php` file
2. Update the following settings:
   ```php
   public $clientId = 'YOUR_CLIENT_ID';
   public $clientSecret = 'YOUR_CLIENT_SECRET';
   public $redirectUri = 'auth/google/callback';
   public $adminRedirectUri = 'admin/google/callback';
   ```
3. The redirect URIs are relative paths that will be automatically converted to absolute URLs by the application
4. If you need to use custom domains or different paths, you can use absolute URLs instead:
   ```php
   public $redirectUri = 'https://yourdomain.com/auth/google/callback';
   public $adminRedirectUri = 'https://yourdomain.com/admin/google/callback';
   ```

## Testing the Integration

1. Start your application
2. Navigate to the login page
3. Click the "Sign in with Google" button
4. You should be redirected to Google's authentication page
5. After successful authentication, you should be redirected back to your application and logged in

## Troubleshooting

- If you encounter a "redirect_uri_mismatch" error, make sure the redirect URI in your application exactly matches one of the authorized redirect URIs in the Google Cloud Console.
- If you get a "invalid_client" error, double-check your client ID and client secret.
- For other issues, check the application logs for detailed error messages.

## Security Considerations

- Never commit your client secret to version control
- Consider using environment variables for sensitive information
- Regularly review and rotate your client secrets
- Implement proper CSRF protection for OAuth callbacks
