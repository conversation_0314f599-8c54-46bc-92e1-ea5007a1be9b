<?php

use Config\JWT as JWTConfig;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;

/**
 * Generate a new JWT token
 *
 * @param array $userData User data to include in the token
 * @return string The JWT token
 */
function generateJWTToken(array $userData): string
{
    $jwtConfig = new JWTConfig();
    
    $issuedAtTime = time();
    $tokenTimeToLive = $jwtConfig->ttl;
    $tokenExpiration = $issuedAtTime + $tokenTimeToLive;
    
    // Token payload
    $payload = [
        'iss' => $jwtConfig->issuer,
        'aud' => $jwtConfig->issuer,
        'iat' => $issuedAtTime,
        'exp' => $tokenExpiration,
        'data' => [
            'id' => $userData['id'],
            'name' => $userData['name'],
            'email' => $userData['email'],
            'is_admin' => $userData['is_admin'] ?? 'user'
        ]
    ];
    
    // Generate token
    return JWT::encode($payload, $jwtConfig->secretKey, 'HS256');
}

/**
 * Validate a JWT token
 *
 * @param string $token The JWT token to validate
 * @return object|false The decoded token payload or false if invalid
 */
function validateJWTToken(string $token)
{
    $jwtConfig = new JWTConfig();
    
    try {
        $decoded = JWT::decode($token, new Key($jwtConfig->secretKey, 'HS256'));
        return $decoded;
    } catch (\Exception $e) {
        log_message('error', 'JWT validation error: ' . $e->getMessage());
        return false;
    }
}

/**
 * Get user data from a JWT token
 *
 * @param string $token The JWT token
 * @return array|false The user data or false if invalid
 */
function getUserFromJWTToken(string $token)
{
    $decoded = validateJWTToken($token);
    
    if (!$decoded) {
        return false;
    }
    
    return (array) $decoded->data;
}

/**
 * Get JWT token from Authorization header
 *
 * @return string|false The JWT token or false if not found
 */
function getJWTFromHeader()
{
    $jwtConfig = new JWTConfig();
    $request = service('request');
    
    $authHeader = $request->getHeaderLine($jwtConfig->tokenHeader);
    
    if (empty($authHeader)) {
        return false;
    }
    
    // Check if the Authorization header contains the Bearer prefix
    if (strpos($authHeader, $jwtConfig->tokenHeaderPrefix) === 0) {
        // Remove the Bearer prefix and any whitespace
        return trim(substr($authHeader, strlen($jwtConfig->tokenHeaderPrefix)));
    }
    
    return false;
}
