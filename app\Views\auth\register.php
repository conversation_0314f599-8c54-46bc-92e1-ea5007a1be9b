<?= $this->extend('templates/main_layout') ?>

<?= $this->section('content') ?>

<style>
    .login-container {
        min-height: 100vh;
        padding: 1.5rem 0;
        background: linear-gradient(135deg, #e6f3ff 0%, #ffffff 100%);
    }

    .auth-card {
        background: rgba(255, 255, 255, 0.98);
        border-radius: 12px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
        padding: 1.5rem;
        transition: all 0.3s ease;
        max-width: 400px;
        margin: auto;
    }

    .auth-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 6px 18px rgba(33, 150, 243, 0.12);
    }

    .auth-title {
        color: #1976d2;
        font-weight: 600;
        font-size: 1.5rem;
        text-align: center;
        margin-bottom: 1.2rem;
    }

    .form-control {
        border-radius: 8px;
        padding: 0.6rem 0.8rem;
        border: 1px solid #e3f2fd;
        transition: all 0.3s ease;
        font-size: 0.95rem;
    }

    .form-control:focus {
        border-color: #2196f3;
        box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.1);
    }

    .btn-primary {
        background: #1976d2;
        border: none;
        border-radius: 8px;
        padding: 0.6rem;
        font-weight: 500;
        transition: all 0.3s ease;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        font-size: 0.9rem;
    }

    .btn-primary:hover {
        background: #1565c0;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(25, 118, 210, 0.2);
    }

    .form-label {
        font-size: 0.9rem;
        font-weight: 500;
        color: #455a64;
    }

    .form-check-label {
        font-size: 0.9rem;
        color: #546e7a;
    }

    .alert {
        border-radius: 8px;
        font-size: 0.9rem;
        padding: 0.75rem 1rem;
    }

    .auth-links {
        margin-top: 1rem;
        text-align: center;
    }

    .auth-links a {
        color: #1976d2;
        text-decoration: none;
        transition: color 0.3s ease;
        font-size: 0.9rem;
    }

    .auth-links a:hover {
        color: #1565c0;
    }
</style>

<div class="login-container d-flex align-items-center justify-content-center">
    <div class="row w-100 justify-content-center">
        <div class="col-md-4 col-sm-10">
            <div class="auth-card">
                <h2 class="auth-title">Register</h2>
            
            <?php if (session()->getFlashdata('error')): ?>
                <div class="alert alert-danger">
                    <?= session()->getFlashdata('error') ?>
                </div>
            <?php endif; ?>
            
            <form action="<?= base_url('auth/register') ?>" method="POST">
                <?= csrf_field() ?>
                
                <div class="mb-3">
                    <label for="name" class="form-label">Full Name</label>
                    <input type="text" class="form-control <?= (isset(session('errors')['name'])) ? 'is-invalid' : '' ?>" id="name" name="name" value="<?= old('name') ?>" required>
                    <?php if (isset(session('errors')['name'])): ?>
                        <div class="invalid-feedback">
                            <?= session('errors')['name'] ?>
                        </div>
                    <?php endif; ?>
                </div>
                
                <div class="mb-3">
                    <label for="email" class="form-label">Email Address</label>
                    <input type="email" class="form-control <?= (isset(session('errors')['email'])) ? 'is-invalid' : '' ?>" id="email" name="email" value="<?= old('email') ?>" required>
                    <?php if (isset(session('errors')['email'])): ?>
                        <div class="invalid-feedback">
                            <?= session('errors')['email'] ?>
                        </div>
                    <?php endif; ?>
                </div>
                
                <div class="mb-3">
                    <label for="password" class="form-label">Password</label>
                    <input type="password" class="form-control <?= (isset(session('errors')['password'])) ? 'is-invalid' : '' ?>" id="password" name="password" required>
                    <?php if (isset(session('errors')['password'])): ?>
                        <div class="invalid-feedback">
                            <?= session('errors')['password'] ?>
                        </div>
                    <?php endif; ?>
                </div>
                
                <div class="mb-3">
                    <label for="confirm_password" class="form-label">Confirm Password</label>
                    <input type="password" class="form-control <?= (isset(session('errors')['confirm_password'])) ? 'is-invalid' : '' ?>" id="confirm_password" name="confirm_password" required>
                    <?php if (isset(session('errors')['confirm_password'])): ?>
                        <div class="invalid-feedback">
                            <?= session('errors')['confirm_password'] ?>
                        </div>
                    <?php endif; ?>
                </div>
                
                <div class="mb-3 form-check">
                    <input type="checkbox" class="form-check-input" id="terms" name="terms" required>
                    <label class="form-check-label" for="terms">I agree to the <a href="#">Terms of Service</a> and <a href="#">Privacy Policy</a></label>
                </div>
                
                <div class="d-grid gap-2">
                    <button type="submit" class="btn btn-primary">Register</button>
                </div>
                
                <div class="auth-links">
                    <p class="mb-0">
                        Already have an account? <a href="<?= base_url('auth/login') ?>">Login</a>
                    </p>
                </div>
            </form>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>