<?= $this->extend('templates/main_layout') ?>

<?= $this->section('content') ?>

<h1 class="mb-4"><?= $category['name'] ?> Papers</h1>
<p class="lead mb-4"><?= $category['description'] ?></p>

<div class="row">
    <div class="col-md-3">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Categories</h5>
            </div>
            <div class="card-body">
                <ul class="list-group list-group-flush">
                    <?php foreach ($categories as $cat): ?>
                        <li class="list-group-item <?= ($cat['id'] == $category['id']) ? 'active' : '' ?>">
                            <a href="<?= base_url('papers/category/' . $cat['id']) ?>" class="text-decoration-none <?= ($cat['id'] == $category['id']) ? 'text-white' : '' ?>">
                                <?= $cat['name'] ?>
                            </a>
                        </li>
                    <?php endforeach; ?>
                </ul>
            </div>
        </div>
    </div>
    
    <div class="col-md-9">
        <div class="row">
            <?php if (empty($papers)): ?>
                <div class="col-12">
                    <div class="alert alert-info">
                        No papers available in this category at the moment. Please check back later.
                    </div>
                </div>
            <?php else: ?>
                <?php foreach ($papers as $paper): ?>
                    <div class="col-md-4 mb-4">
                        <div class="card h-100">
                            <div class="card-body">
                                <h5 class="card-title"><?= $paper['title'] ?></h5>
                                <p class="card-text"><?= substr($paper['description'], 0, 100) ?>...</p>
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="price-tag">$<?= number_format($paper['price'], 2) ?></span>
                                    <a href="<?= base_url('papers/view/' . $paper['id']) ?>" class="btn btn-sm btn-primary">View Details</a>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>
</div>

<?= $this->endSection() ?>