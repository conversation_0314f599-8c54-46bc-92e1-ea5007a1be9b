<?= $this->extend('templates/admin_layout') ?>

<?= $this->section('content') ?>

<div class="container-fluid">
    <h1 class="h3 mb-4 text-gray-800">Add New Paper</h1>
    
    <div class="card">
        <div class="card-body">
            <?php if(session()->has('error')): ?>
                <div class="alert alert-danger">
                    <?= session('error') ?>
                </div>
            <?php endif; ?>
            
            <?php if(session()->has('success')): ?>
                <div class="alert alert-success">
                    <?= session('success') ?>
                </div>
            <?php endif; ?>
            
            <form action="<?= base_url('admin/papers/add') ?>" method="post" enctype="multipart/form-data">
                <?= csrf_field() ?>
                
                <div class="form-group">
                    <label for="title">Title</label>
                    <input type="text" class="form-control <?= (isset($validation) && $validation->hasError('title')) ? 'is-invalid' : '' ?>" 
                           id="title" name="title" value="<?= old('title') ?>" required>
                    <?php if(isset($validation) && $validation->hasError('title')): ?>
                        <div class="invalid-feedback">
                            <?= $validation->getError('title') ?>
                        </div>
                    <?php endif; ?>
                </div>
                
                <div class="form-group">
                    <label for="category_id">Category</label>
                    <select class="form-control <?= (isset($validation) && $validation->hasError('category_id')) ? 'is-invalid' : '' ?>" 
                            id="category_id" name="category_id" required>
                        <option value="">Select Category</option>
                        <?php foreach($categories as $category): ?>
                            <option value="<?= $category['id'] ?>" <?= old('category_id') == $category['id'] ? 'selected' : '' ?>>
                                <?= $category['name'] ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                    <?php if(isset($validation) && $validation->hasError('category_id')): ?>
                        <div class="invalid-feedback">
                            <?= $validation->getError('category_id') ?>
                        </div>
                    <?php endif; ?>
                </div>
                
                <div class="form-group">
                    <label for="description">Description</label>
                    <textarea class="form-control <?= (isset($validation) && $validation->hasError('description')) ? 'is-invalid' : '' ?>" 
                              id="description" name="description" rows="5" required><?= old('description') ?></textarea>
                    <?php if(isset($validation) && $validation->hasError('description')): ?>
                        <div class="invalid-feedback">
                            <?= $validation->getError('description') ?>
                        </div>
                    <?php endif; ?>
                </div>
                
                <div class="form-group">
                    <label for="price">Price ($)</label>
                    <input type="number" step="0.01" class="form-control <?= (isset($validation) && $validation->hasError('price')) ? 'is-invalid' : '' ?>" 
                           id="price" name="price" value="<?= old('price') ?>" required>
                    <?php if(isset($validation) && $validation->hasError('price')): ?>
                        <div class="invalid-feedback">
                            <?= $validation->getError('price') ?>
                        </div>
                    <?php endif; ?>
                </div>
                
                <div class="form-group">
                    <label for="paper_file">Paper File (PDF)</label>
                    <input type="file" class="form-control-file <?= (isset($validation) && $validation->hasError('paper_file')) ? 'is-invalid' : '' ?>" 
                           id="paper_file" name="paper_file" accept=".pdf" required>
                    <?php if(isset($validation) && $validation->hasError('paper_file')): ?>
                        <div class="invalid-feedback">
                            <?= $validation->getError('paper_file') ?>
                        </div>
                    <?php endif; ?>
                </div>
                
                <div class="form-group">
                    <label for="preview_file">Preview File (PDF)</label>
                    <input type="file" class="form-control-file <?= (isset($validation) && $validation->hasError('preview_file')) ? 'is-invalid' : '' ?>" 
                           id="preview_file" name="preview_file"  required>
                    <?php if(isset($validation) && $validation->hasError('preview_file')): ?>
                        <div class="invalid-feedback">
                            <?= $validation->getError('preview_file') ?>
                        </div>
                    <?php endif; ?>
                </div>

                <div class="form-group">
                    <label for="status">Status</label>
                    <select class="form-control" id="status" name="status">
                        <option value="active" <?= old('status') == 'active' ? 'selected' : '' ?>>Active</option>
                        <option value="inactive" <?= old('status') == 'inactive' ? 'selected' : '' ?>>Inactive</option>
                    </select>
                </div>
                
                <button type="submit" class="btn btn-primary">Add Paper</button>
                <a href="<?= base_url('admin/papers') ?>" class="btn btn-secondary">Cancel</a>
            </form>
        </div>
    </div>
</div>

<?= $this->endSection() ?>