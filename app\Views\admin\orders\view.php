<?= $this->extend('templates/admin_layout') ?>

<?= $this->section('content') ?>
<div class="container-fluid px-4">
    <h1 class="mt-4"><?= $title ?></h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="<?= base_url('admin/dashboard') ?>">Dashboard</a></li>
        <li class="breadcrumb-item"><a href="<?= base_url('admin/orders') ?>">Orders</a></li>
        <li class="breadcrumb-item active">View Order</li>
    </ol>

    <?php if (session()->getFlashdata('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?= session()->getFlashdata('success') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <?php if (session()->getFlashdata('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?= session()->getFlashdata('error') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="m-0 font-weight-bold">Order Details</h5>
                </div>
                <div class="card-body">
                    <table class="table">
                        <tr>
                            <th>Order ID</th>
                            <td>#<?= $order['id'] ?></td>
                        </tr>
                        <tr>
                            <th>Customer</th>
                            <td><?= esc($order['user_name']) ?></td>
                        </tr>
                        <tr>
                            <th>Email</th>
                            <td><?= esc($order['user_email']) ?></td>
                        </tr>
                        <tr>
                            <th>Total Amount</th>
                            <td><?= number_format($order['total_amount'], 2) ?></td>
                        </tr>
                        <tr>
                            <th>Payment Method</th>
                            <td><?= ucfirst($order['payment_method'] ?? 'N/A') ?></td>
                        </tr>
                        <tr>
                            <th>Transaction ID</th>
                            <td><?= $order['transaction_id'] ?? 'N/A' ?></td>
                        </tr>
                        <tr>
                            <th>Status</th>
                            <td>
                                <?php if ($order['payment_status'] == 'completed'): ?>
                                    <span class="badge bg-success">Completed</span>
                                <?php elseif ($order['payment_status'] == 'pending'): ?>
                                    <span class="badge bg-warning">Pending</span>
                                <?php else: ?>
                                    <span class="badge bg-danger">Failed</span>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <tr>
                            <th>Date</th>
                            <td><?= date('F j, Y g:i A', strtotime($order['created_at'])) ?></td>
                        </tr>
                    </table>
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="m-0 font-weight-bold">Update Status</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex gap-2">
                        <a href="<?= base_url('admin/orders/update-status/' . $order['id'] . '/pending') ?>" class="btn btn-warning">
                            Mark as Pending
                        </a>
                        <a href="<?= base_url('admin/orders/update-status/' . $order['id'] . '/completed') ?>" class="btn btn-success">
                            Mark as Completed
                        </a>
                        <a href="<?= base_url('admin/orders/update-status/' . $order['id'] . '/failed') ?>" class="btn btn-danger">
                            Mark as Failed
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="m-0 font-weight-bold">Order Items</h5>
                </div>
                <div class="card-body">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Item</th>
                                <th>Preview</th>
                                <th>Price</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($orderItems as $item): ?>
                                <tr>
                                    <td><?= esc($item['paper_title']) ?></td>
                                    <td>
                                        <?php if ($item['preview_path']): ?>
                                            <img src="<?= base_url("uploads/papers/previews/" . $item['preview_path']) ?>" alt="<?= esc($item['paper_title']) ?>" style="max-width: 50px;">
                                        <?php else: ?>
                                            <span class="text-muted">No preview</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?= number_format($item['price'], 2) ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                        <tfoot>
                            <tr>
                                <th colspan="2" class="text-end">Total:</th>
                                <th><?= number_format($order['total_amount'], 2) ?></th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>