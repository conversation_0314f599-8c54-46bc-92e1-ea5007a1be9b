<?= $this->extend('templates/main_layout') ?>

<?= $this->section('content') ?>
<!-- Checkout Header -->
<div class="bg-light py-4 border-bottom">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h1 class="h3 fw-bold mb-0">Secure Checkout</h1>
            </div>
            <div class="col-md-6">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb mb-0 justify-content-md-end">
                        <li class="breadcrumb-item"><a href="<?= base_url() ?>" class="text-decoration-none">Home</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('cart') ?>" class="text-decoration-none">Cart</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Checkout</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>
</div>

<!-- Checkout Progress -->
<div class="bg-white py-3 border-bottom mb-4 d-none d-md-block">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-center">
                    <div class="checkout-progress">
                        <div class="checkout-progress-step active">
                            <div class="checkout-progress-step-icon">
                                <i class="fas fa-shopping-cart"></i>
                            </div>
                            <div class="checkout-progress-step-text">Cart</div>
                        </div>
                        <div class="checkout-progress-connector active"></div>
                        <div class="checkout-progress-step active">
                            <div class="checkout-progress-step-icon">
                                <i class="fas fa-credit-card"></i>
                            </div>
                            <div class="checkout-progress-step-text">Checkout</div>
                        </div>
                        <div class="checkout-progress-connector"></div>
                        <div class="checkout-progress-step">
                            <div class="checkout-progress-step-icon">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="checkout-progress-step-text">Confirmation</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- One-Page Checkout -->
<section class="py-5">
    <div class="container">
        <?php
        // Calculate tax and final total
        $tax = $total * 0.08; // 8% tax rate
        $finalTotal = $total + $tax;
        ?>

        <form action="<?= base_url('cart/process') ?>" method="post" id="checkout-form">
            <?= csrf_field() ?>
            <input type="hidden" name="payment_method" value="razorpay">

            <div class="row g-4">
                <!-- Left Column - Checkout Form -->
                <div class="col-lg-8">
                    <div class="one-page-checkout">
                        <!-- Order Review Section -->
                        <div class="checkout-section">
                            <div class="checkout-section-header">
                                <div class="checkout-section-number">1</div>
                                <h3 class="checkout-section-title">Review Your Order</h3>
                            </div>
                            <div class="bg-light p-3 rounded mb-4">
                                <div class="d-flex align-items-center text-primary mb-3">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <span class="fw-medium">Review your order details before proceeding to payment</span>
                                </div>
                            </div>

                            <?php foreach ($items as $item): ?>
                                <div class="card border-0 shadow-sm mb-3">
                                    <div class="card-body p-0">
                                        <div class="row g-0">
                                            <!-- Product Image -->
                                            <div class="col-md-3 col-sm-4">
                                                <?php if (!empty($item['preview_path'])): ?>
                                                    <div class="product-image mx-auto m-3" style="width: 100px; height: 100px;">
                                                        <img src="<?= base_url('uploads/papers/previews/' . $item['preview_path']) ?>"
                                                            alt="<?= esc($item['title']) ?>"
                                                            class="img-fluid rounded shadow-sm">
                                                    </div>
                                                <?php else: ?>
                                                    <div class="product-image bg-light d-flex align-items-center justify-content-center mx-auto m-3" style="width: 100px; height: 100px;">
                                                        <i class="fas fa-file-alt text-primary fa-2x"></i>
                                                    </div>
                                                <?php endif; ?>
                                            </div>

                                            <!-- Product Details -->
                                            <div class="col-md-6 col-sm-8 py-3 px-4">
                                                <h5 class="fw-bold mb-2"><?= esc($item['title']) ?></h5>
                                                <div class="d-flex align-items-center mb-2">
                                                    <span class="badge bg-primary bg-opacity-10 text-primary me-2">Digital Download</span>
                                                    <?php if (isset($item['category_name'])): ?>
                                                        <span class="badge bg-secondary bg-opacity-10 text-secondary"><?= $item['category_name'] ?></span>
                                                    <?php endif; ?>
                                                </div>
                                                <p class="text-muted small mb-0">
                                                    <i class="fas fa-check-circle text-success me-1"></i> Instant download after purchase
                                                </p>
                                                <p class="text-muted small mb-0">
                                                    <i class="fas fa-check-circle text-success me-1"></i> Full access to all content
                                                </p>
                                            </div>

                                            <!-- Price -->
                                            <div class="col-md-3 d-flex align-items-center justify-content-center bg-light py-3">
                                                <div class="text-center">
                                                    <span class="d-block text-muted small">Price</span>
                                                    <span class="h4 fw-bold text-primary mb-0">₹<?= number_format($item['price'], 2) ?></span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>

                        <!-- Customer Information Section -->
                        <div class="checkout-section">
                            <div class="checkout-section-header">
                                <div class="checkout-section-number">2</div>
                                <h3 class="checkout-section-title">Customer Information</h3>
                            </div>
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label for="name" class="form-label fw-medium">Full Name</label>
                                    <div class="input-group">
                                        <span class="input-group-text bg-light"><i class="fas fa-user text-primary"></i></span>
                                        <input type="text" class="form-control" id="name" name="name" value="<?= esc(session()->get('name')) ?>" readonly>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <label for="email" class="form-label fw-medium">Email Address</label>
                                    <div class="input-group">
                                        <span class="input-group-text bg-light"><i class="fas fa-envelope text-primary"></i></span>
                                        <input type="email" class="form-control" id="email" name="email" value="<?= esc(session()->get('email')) ?>" readonly>
                                    </div>
                                    <div class="form-text small text-muted mt-1">
                                        Your receipt and download link will be sent to this email
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <label for="phone" class="form-label fw-medium">Phone Number (Optional)</label>
                                    <div class="input-group">
                                        <span class="input-group-text bg-light"><i class="fas fa-phone text-primary"></i></span>
                                        <input type="tel" class="form-control" id="phone" name="phone" placeholder="For order updates">
                                    </div>
                                </div>

                                <div class="col-12 mt-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="receive_updates" name="receive_updates" checked>
                                        <label class="form-check-label" for="receive_updates">
                                            Send me updates about new papers and special offers
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Payment Method Section -->
                        <div class="checkout-section">
                            <div class="checkout-section-header">
                                <div class="checkout-section-number">3</div>
                                <h3 class="checkout-section-title">Payment Method</h3>
                            </div>

                            <div class="payment-methods">
                                <div class="payment-method active">
                                    <input type="radio" name="payment_method" id="razorpay" value="razorpay" class="payment-method-radio" checked>
                                    <img src="<?= base_url('public/assets/images/razorpay-logo.png') ?>" alt="Razorpay" class="payment-method-logo" onerror="this.src='https://via.placeholder.com/120x40?text=Razorpay'; this.onerror=null;">
                                    <div class="payment-method-info">
                                        <h6 class="payment-method-title">Pay with Razorpay</h6>
                                        <p class="payment-method-description">Safe and secure payment via credit/debit card, UPI, or net banking</p>
                                    </div>
                                </div>




                            </div>



                            <div id="payment-error" class="alert alert-danger mt-4 border-start border-4 border-danger" style="display: none;">
                                <div class="d-flex">
                                    <div class="me-3">
                                        <i class="fas fa-exclamation-circle text-danger fa-2x"></i>
                                    </div>
                                    <div>
                                        <h6 class="fw-bold mb-2">Payment Failed</h6>
                                        <p class="mb-0">There was an issue processing your payment. Please try again or use a different payment method.</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="d-flex justify-content-between align-items-center mt-4">
                            <a href="<?= base_url('cart') ?>" class="btn btn-outline-secondary px-4">
                                <i class="fas fa-arrow-left me-2"></i>Back to Cart
                            </a>
                            <button type="button" id="razorpay-button" class="btn btn-primary btn-lg px-4 py-3">
                                <i class="fas fa-lock me-2"></i>Complete Purchase - ₹<?= number_format($finalTotal, 2) ?>
                            </button>
                        </div>
                        <div class="text-center mt-3">
                            <small class="text-muted">
                                By completing your purchase, you agree to our
                                <a href="#" class="text-decoration-none">Terms of Service</a> and
                                <a href="#" class="text-decoration-none">Privacy Policy</a>
                            </small>
                        </div>
                    </div>
                </div>

                <!-- Right Column - Order Summary -->
                <div class="col-lg-4">
                    <div class="checkout-summary">
                        <h4 class="checkout-summary-title">
                            <i class="fas fa-receipt text-primary me-2"></i>Order Summary
                        </h4>

                        <div class="checkout-summary-items">
                            <?php foreach ($items as $item): ?>
                                <div class="checkout-summary-item">
                                    <div class="checkout-summary-item-title">
                                        <?= esc($item['title']) ?>
                                        <div class="badge bg-primary bg-opacity-10 text-primary mt-1">Digital Download</div>
                                    </div>
                                    <div class="checkout-summary-item-price">₹<?= number_format($item['price'], 2) ?></div>
                                </div>
                            <?php endforeach; ?>
                        </div>

                        <hr>

                        <div class="checkout-summary-item">
                            <div class="text-muted">Subtotal</div>
                            <div class="fw-bold">₹<?= number_format($total, 2) ?></div>
                        </div>

                        <div class="checkout-summary-item">
                            <div class="text-muted">Tax (8%)</div>
                            <div>₹<?= number_format($tax, 2) ?></div>
                        </div>

                        <div class="checkout-summary-total">
                            <div>Total</div>
                            <div>₹<?= number_format($finalTotal, 2) ?></div>
                        </div>

                        <div class="bg-light p-3 rounded mt-4">
                            <div class="d-flex align-items-center mb-3">
                                <div class="d-flex align-items-center justify-content-center bg-primary bg-opacity-10 rounded-circle me-3" style="width: 36px; height: 36px;">
                                    <i class="fas fa-shield-alt text-primary"></i>
                                </div>
                                <span class="small fw-medium">Secure 256-bit SSL encryption</span>
                            </div>
                            <div class="d-flex align-items-center mb-3">
                                <div class="d-flex align-items-center justify-content-center bg-primary bg-opacity-10 rounded-circle me-3" style="width: 36px; height: 36px;">
                                    <i class="fas fa-download text-primary"></i>
                                </div>
                                <span class="small fw-medium">Instant download after payment</span>
                            </div>
                            <div class="d-flex align-items-center">
                                <div class="d-flex align-items-center justify-content-center bg-primary bg-opacity-10 rounded-circle me-3" style="width: 36px; height: 36px;">
                                    <i class="fas fa-headset text-primary"></i>
                                </div>
                                <span class="small fw-medium">24/7 Customer Support</span>
                            </div>
                        </div>

                        <div class="mt-4 pt-3 border-top text-center">
                            <h6 class="fw-bold small text-uppercase text-muted mb-3">We Accept</h6>
                            <div class="d-flex justify-content-between">
                                <i class="fab fa-cc-visa fa-2x text-primary"></i>
                                <i class="fab fa-cc-mastercard fa-2x text-primary"></i>
                                <i class="fab fa-cc-amex fa-2x text-primary"></i>
                                <i class="fab fa-cc-paypal fa-2x text-primary"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</section>

<!-- Razorpay JavaScript SDK -->
<script src="https://checkout.razorpay.com/v1/checkout.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Handle payment method selection
        const paymentMethods = document.querySelectorAll('.payment-method');
        paymentMethods.forEach(method => {
            method.addEventListener('click', function() {
                // Remove active class from all methods
                paymentMethods.forEach(m => m.classList.remove('active'));
                // Add active class to clicked method
                this.classList.add('active');
                // Select the radio button
                const radio = this.querySelector('input[type="radio"]');
                radio.checked = true;
            });
        });

        // Handle Razorpay payment
        document.getElementById('razorpay-button').onclick = function() {
            // Create a new Razorpay instance
            var options = {
                "key": "rzp_test_XaZ89XsD6ejHqt", // Your Razorpay Key ID
                "test": true, // Enable test mode
                "amount": <?= $finalTotal * 100 ?>, // Amount in paise
                "currency": "INR",
                "name": "Sample Paper Store",
                "description": "Purchase of Sample Papers",
                "image": "<?= base_url('assets/images/logo.png') ?>",
                "handler": function(response) {
                    console.log('Payment successful:', response);
                    // Add the payment ID to the form
                    var form = document.getElementById('checkout-form');
                    var hiddenField = document.createElement('input');
                    hiddenField.type = 'hidden';
                    hiddenField.name = 'razorpay_payment_id';
                    hiddenField.value = response.razorpay_payment_id;
                    form.appendChild(hiddenField);

                    // Submit the form
                    form.submit();
                },
                "prefill": {
                    "name": "<?= esc(session()->get('name')) ?>",
                    "email": "<?= esc(session()->get('email')) ?>"
                },
                "theme": {
                    "color": "#4361ee"
                },
                "modal": {
                    "ondismiss": function() {
                        console.log('Checkout form closed');
                        document.getElementById('payment-error').style.display = 'block';
                    }
                }
            };

            try {
                var rzp = new Razorpay(options);
                rzp.on('payment.failed', function(response) {
                    console.error('Payment failed:', response.error);
                    document.getElementById('payment-error').innerHTML = '<p class="mb-0"><i class="fas fa-exclamation-circle me-2"></i>Payment failed: ' + response.error.description + '</p>';
                    document.getElementById('payment-error').style.display = 'block';
                });
                rzp.open();
            } catch (error) {
                console.error('Razorpay error:', error);
                document.getElementById('payment-error').innerHTML = '<p class="mb-0"><i class="fas fa-exclamation-circle me-2"></i>Error initializing payment: ' + error.message + '</p>';
                document.getElementById('payment-error').style.display = 'block';
            }
        };
    });
</script>
<?= $this->endSection() ?>