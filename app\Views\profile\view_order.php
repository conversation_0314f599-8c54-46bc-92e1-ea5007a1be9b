<?= $this->extend('templates/main_layout') ?>

<?= $this->section('content') ?>
<div class="container py-5">
    <div class="row">
        <div class="col-lg-3">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">My Account</h5>
                </div>
                <div class="list-group list-group-flush">
                    <a href="<?= base_url('profile') ?>" class="list-group-item list-group-item-action">
                        <i class="fas fa-user me-2"></i>Profile
                    </a>
                    <a href="<?= base_url('profile/orders') ?>" class="list-group-item list-group-item-action active">
                        <i class="fas fa-shopping-bag me-2"></i>Orders
                    </a>
                    <a href="<?= base_url('profile/downloads') ?>" class="list-group-item list-group-item-action">
                        <i class="fas fa-download me-2"></i>Downloads
                    </a>
                    <a href="<?= base_url('auth/logout') ?>" class="list-group-item list-group-item-action text-danger">
                        <i class="fas fa-sign-out-alt me-2"></i>Logout
                    </a>
                </div>
            </div>
        </div>

        <div class="col-lg-9">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Order #<?= $order['id'] ?></h5>
                    <a href="<?= base_url('profile/orders') ?>" class="btn btn-light btn-sm">
                        <i class="fas fa-arrow-left me-1"></i>Back to Orders
                    </a>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h6>Order Details</h6>
                            <p><strong>Order Date:</strong> <?= date('F j, Y g:i A', strtotime($order['created_at'])) ?></p>
                            <p><strong>Order Total:</strong> <?= number_format($order['total_amount'], 2) ?></p>
                            <p><strong>Payment Method:</strong> <?= ucfirst($order['payment_method'] ?? 'N/A') ?></p>
                            <p><strong>Transaction ID:</strong> <?= $order['transaction_id'] ?? 'N/A' ?></p>
                        </div>
                        <div class="col-md-6">
                            <h6>Payment Status</h6>
                            <?php if ($order['payment_status'] == 'completed'): ?>
                                <div class="alert alert-success">
                                    <p class="mb-0"><i class="fas fa-check-circle me-2"></i>Payment Completed</p>
                                </div>
                                <p>Your payment has been processed successfully. You can now download your purchased papers.</p>
                            <?php elseif ($order['payment_status'] == 'pending'): ?>
                                <div class="alert alert-warning">
                                    <p class="mb-0"><i class="fas fa-clock me-2"></i>Payment Pending</p>
                                </div>
                                <p>Your payment is being processed. You will be able to download your papers once the payment is completed.</p>
                                <?php if ($order['payment_method'] === 'bank_transfer'): ?>
                                    <div class="mt-3">
                                        <p><strong>Bank Transfer Details:</strong></p>
                                        <p><strong>Bank Name:</strong> Example Bank</p>
                                        <p><strong>Account Number:</strong> **********</p>
                                        <p><strong>IFSC Code:</strong> EXBK0001234</p>
                                        <p><strong>Account Name:</strong> Sample Paper Store</p>
                                        <p><strong>Reference:</strong> Order #<?= $order['id'] ?></p>
                                    </div>
                                <?php endif; ?>
                            <?php else: ?>
                                <div class="alert alert-danger">
                                    <p class="mb-0"><i class="fas fa-times-circle me-2"></i>Payment Failed</p>
                                </div>
                                <p>There was an issue processing your payment. Please contact customer support for assistance.</p>
                            <?php endif; ?>
                        </div>
                    </div>

                    <h6>Order Items</h6>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Item</th>
                                    <th>Preview</th>
                                    <th>Price</th>
                                    <?php if ($order['payment_status'] == 'completed'): ?>
                                        <th>Action</th>
                                    <?php endif; ?>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($orderItems as $item): ?>
                                    <tr>
                                        <td><?= esc($item['paper_title']) ?></td>
                                        <td>
                                            <?php if ($item['preview_path']): ?>
                                                <img src="<?= base_url($item['preview_path']) ?>" alt="<?= esc($item['paper_title']) ?>" style="max-width: 50px;">
                                            <?php else: ?>
                                                <span class="text-muted">No preview</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?= number_format($item['price'], 2) ?></td>
                                        <?php if ($order['payment_status'] == 'completed'): ?>
                                            <td>
                                                <a href="<?= base_url('profile/download/' . $item['paper_id']) ?>" class="btn btn-success btn-sm" target="_blank">
                                                    <i class="fas fa-download"></i> Download
                                                </a>
                                            </td>
                                        <?php endif; ?>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                            <tfoot>
                                <tr>
                                    <th colspan="<?= $order['payment_status'] == 'completed' ? '3' : '2' ?>" class="text-end">Total:</th>
                                    <th><?= number_format($order['total_amount'], 2) ?></th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>
