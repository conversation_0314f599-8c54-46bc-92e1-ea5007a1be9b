<?php

namespace App\Controllers;

use App\Models\CategoryModel;
use App\Models\PaperModel;

class Home extends BaseController
{
    public function index()
    {
        $categoryModel = new CategoryModel();

        // Get popular categories (for now, just get all)
        $categories = $categoryModel->findAll();


        $data = [
            'title' => 'Home - Sample Paper Store',
            'categories' => $categories,
        ];

        return view('home', $data);
    }

    public function about()
    {
        $data = [
            'title' => 'About Us - Sample Paper Store'
        ];

        return view('about', $data);
    }

    public function contact()
    {
        $data = [
            'title' => 'Contact Us - Sample Paper Store'
        ];

        return view('contact', $data);
    }

    public function faq()
    {
        $data = [
            'title' => 'FAQs - Sample Paper Store'
        ];

        return view('faq', $data);
    }

    public function submitContact()
    {
        // Validate the form data
        $rules = [
            'name' => 'required|min_length[3]|max_length[100]',
            'email' => 'required|valid_email',
            'subject' => 'required|min_length[5]|max_length[100]',
            'message' => 'required|min_length[10]'
        ];

        if (!$this->validate($rules)) {
            return redirect()->to('contact')->with('error', 'Please check your input and try again.');
        }

        // Get the form data
        $name = $this->request->getPost('name');
        $email = $this->request->getPost('email');
        $subject = $this->request->getPost('subject');
        $message = $this->request->getPost('message');

        // In a real application, you would save this to a database or send an email
        // For now, we'll just log it and return a success message
        log_message('info', "Contact form submission from {$name} ({$email}): {$subject}");

        return redirect()->to('contact')->with('success', 'Thank you for your message! We will get back to you soon.');
    }
}
