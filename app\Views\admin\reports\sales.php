<?= $this->extend('templates/admin_layout') ?>

<?= $this->section('content') ?>
<div class="container-fluid px-4">
    <h1 class="mt-4"><?= $title ?></h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="<?= base_url('admin/dashboard') ?>">Dashboard</a></li>
        <li class="breadcrumb-item active">Sales Reports</li>
    </ol>
    
    <div class="row">
        <div class="col-xl-3 col-md-6">
            <div class="card bg-primary text-white mb-4">
                <div class="card-body">
                    <h2 class="mb-0"><?= number_format($totalSales, 2) ?></h2>
                    <div>Total Sales</div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-success text-white mb-4">
                <div class="card-body">
                    <h2 class="mb-0"><?= $totalOrders ?></h2>
                    <div>Completed Orders</div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-warning text-white mb-4">
                <div class="card-body">
                    <h2 class="mb-0"><?= $pendingOrders ?></h2>
                    <div>Pending Orders</div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-info text-white mb-4">
                <div class="card-body">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>Export Reports</div>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-light dropdown-toggle" type="button" id="exportDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                Export
                            </button>
                            <ul class="dropdown-menu" aria-labelledby="exportDropdown">
                                <li><a class="dropdown-item" href="<?= base_url('admin/reports/generate/sales') ?>">Sales Report</a></li>
                                <li><a class="dropdown-item" href="<?= base_url('admin/reports/generate/users') ?>">Users Report</a></li>
                                <li><a class="dropdown-item" href="<?= base_url('admin/reports/generate/papers') ?>">Papers Report</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-xl-8">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-chart-bar me-1"></i>
                    Sales Last 30 Days
                </div>
                <div class="card-body">
                    <canvas id="salesChart" width="100%" height="40"></canvas>
                </div>
            </div>
        </div>
        <div class="col-xl-4">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-chart-pie me-1"></i>
                    Top Selling Papers
                </div>
                <div class="card-body">
                    <canvas id="topPapersChart" width="100%" height="50"></canvas>
                </div>
            </div>
        </div>
    </div>
    
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-table me-1"></i>
            Daily Sales Data
        </div>
        <div class="card-body">
            <table id="salesTable" class="table table-striped">
                <thead>
                    <tr>
                        <th>Date</th>
                        <th>Orders</th>
                        <th>Total Sales</th>
                        <th>Average Order Value</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($salesData as $data): ?>
                        <tr>
                            <td><?= date('M d, Y', strtotime($data['sale_date'])) ?></td>
                            <td><?= $data['order_count'] ?></td>
                            <td><?= number_format($data['daily_total'], 2) ?></td>
                            <td><?= number_format($data['daily_total'] / $data['order_count'], 2) ?></td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
    
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-table me-1"></i>
            Top Selling Papers
        </div>
        <div class="card-body">
            <table id="topPapersTable" class="table table-striped">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Title</th>
                        <th>Orders</th>
                        <th>Revenue</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($topPapers as $paper): ?>
                        <tr>
                            <td><?= $paper['id'] ?></td>
                            <td><?= esc($paper['title']) ?></td>
                            <td><?= $paper['order_count'] ?></td>
                            <td><?= number_format($paper['total_revenue'], 2) ?></td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Sales Chart
        const salesData = <?= json_encode($salesData) ?>;
        const dates = salesData.map(item => item.sale_date).reverse();
        const totals = salesData.map(item => item.daily_total).reverse();
        
        const salesCtx = document.getElementById('salesChart').getContext('2d');
        new Chart(salesCtx, {
            type: 'line',
            data: {
                labels: dates,
                datasets: [{
                    label: 'Daily Sales',
                    data: totals,
                    backgroundColor: 'rgba(0, 123, 255, 0.2)',
                    borderColor: 'rgba(0, 123, 255, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
        
        // Top Papers Chart
        const topPapers = <?= json_encode($topPapers) ?>;
        const paperTitles = topPapers.map(item => item.title);
        const paperRevenues = topPapers.map(item => item.total_revenue);
        
        const topPapersCtx = document.getElementById('topPapersChart').getContext('2d');
        new Chart(topPapersCtx, {
            type: 'pie',
            data: {
                labels: paperTitles,
                datasets: [{
                    data: paperRevenues,
                    backgroundColor: [
                        'rgba(255, 99, 132, 0.7)',
                        'rgba(54, 162, 235, 0.7)',
                        'rgba(255, 206, 86, 0.7)',
                        'rgba(75, 192, 192, 0.7)',
                        'rgba(153, 102, 255, 0.7)',
                        'rgba(255, 159, 64, 0.7)',
                        'rgba(199, 199, 199, 0.7)',
                        'rgba(83, 102, 255, 0.7)',
                        'rgba(40, 159, 64, 0.7)',
                        'rgba(210, 199, 199, 0.7)'
                    ],
                    borderWidth: 1
                }]
            }
        });
        
        // Initialize DataTables
        $('#salesTable').DataTable({
            paging: true,
            searching: true,
            ordering: true,
            info: true
        });
        
        $('#topPapersTable').DataTable({
            paging: true,
            searching: true,
            ordering: true,
            info: true
        });
    });
</script>
<?= $this->endSection() ?>
