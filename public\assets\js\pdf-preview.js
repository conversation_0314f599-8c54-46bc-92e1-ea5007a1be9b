// PDF Preview Handler
const previewModal = document.getElementById('previewModal');
if (previewModal) {
    const previewFrame = document.getElementById('previewFrame');
    const previewButtons = document.querySelectorAll('.preview-paper');

    // Initialize PDF.js viewer
    previewButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const previewUrl = this.getAttribute('data-preview-url');
            if (previewUrl) {
                // Set up PDF viewer URL with PDF.js
                const viewerUrl = '/assets/pdfjs/web/viewer.html?file=' + encodeURIComponent(previewUrl);
                previewFrame.src = viewerUrl;
                
                // Show modal
                const modal = new bootstrap.Modal(previewModal);
                modal.show();
            }
        });
    });

    // Clear iframe source when modal is closed
    previewModal.addEventListener('hidden.bs.modal', function () {
        previewFrame.src = '';
    });
}