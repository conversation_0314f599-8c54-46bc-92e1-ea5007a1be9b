<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Models\CategoryModel;

class Categories extends BaseController
{
    protected $categoryModel;

    public function __construct()
    {
        $this->categoryModel = new CategoryModel();
    }

    public function index()
    {
        $data = [
            'title' => 'Categories Management',
            'categories' => $this->categoryModel->findAll()
        ];
        return view('admin/categories/index', $data);
    }

    public function create()
    { 
 

        if ($this->request->getMethod() === 'POST'){ 
       
            $rules = [
                'name' => 'required|min_length[3]|max_length[255]',
                'description' => 'required',
                'preview_image' => 'uploaded[preview_image]|is_image[preview_image]|mime_in[preview_image,image/jpg,image/jpeg,image/png]'
            ];

            if ($this->validate($rules)) {
                $img = $this->request->getFile('preview_image');
                $newName = $img->getRandomName();
                $img->move('uploads/categories', $newName);

                $this->categoryModel->save([
                    'name' => $this->request->getPost('name'),
                    'description' => $this->request->getPost('description'),
                    'preview_image' => 'uploads/categories/' . $newName
                ]);

                session()->setFlashdata('success', 'Category created successfully');
                return redirect()->to('/admin/categories');
            } else {
                return view('admin/categories/create', [
                    'title' => 'Create Category',
                    'validation' => $this->validator
                ]);
            }
        }

        return view('admin/categories/create', [
            'title' => 'Create Category'
        ]);
    }

    public function edit($id = null)
    {
        $category = $this->categoryModel->find($id);
        if ($category === null) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Category not found');
        }

        if ($this->request->getMethod() === 'POST') {
            $rules = [
                'name' => 'required|min_length[3]|max_length[255]',
                'description' => 'required'
            ];

            if ($this->request->getFile('preview_image')->isValid()) {
                $rules['preview_image'] = 'uploaded[preview_image]|is_image[preview_image]|mime_in[preview_image,image/jpg,image/jpeg,image/png]';
            }

            if ($this->validate($rules)) {
                $data = [
                    'name' => $this->request->getPost('name'),
                    'description' => $this->request->getPost('description')
                ];

                if ($this->request->getFile('preview_image')->isValid()) {
                    $img = $this->request->getFile('preview_image');
                    $newName = $img->getRandomName();
                    $img->move('uploads/categories', $newName);
                    $data['preview_image'] = 'uploads/categories/' . $newName;

                    // Delete old image
                    if (file_exists($category['preview_image'])) {
                        unlink($category['preview_image']);
                    }
                }

                $this->categoryModel->update($id, $data);
                session()->setFlashdata('success', 'Category updated successfully');
                return redirect()->to('/admin/categories');
            } else {
                return view('admin/categories/edit', [
                    'title' => 'Edit Category',
                    'category' => $category,
                    'validation' => $this->validator
                ]);
            }
        }

        return view('admin/categories/edit', [
            'title' => 'Edit Category',
            'category' => $category
        ]);
    }

    public function delete($id = null)
    {
        $category = $this->categoryModel->find($id);
        if ($category === null) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Category not found');
        }

        if (file_exists($category['preview_image'])) {
            unlink($category['preview_image']);
        }

        $this->categoryModel->delete($id);
        session()->setFlashdata('success', 'Category deleted successfully');
        return redirect()->to('/admin/categories');
    }
}