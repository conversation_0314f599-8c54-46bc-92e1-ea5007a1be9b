<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Models\UserModel;
use App\Models\PaperModel;
use App\Models\OrderModel;

class Dashboard extends BaseController
{
    public function index()
    {
        $userModel = new UserModel();
        $paperModel = new PaperModel();
        $orderModel = new OrderModel();
        
        $data = [
            'title' => 'Admin Dashboard',
            'totalUsers' => $userModel->countAll(),
            'totalPapers' => $paperModel->countAll(),
            'totalOrders' => $orderModel->countAll(),
            'recentUsers' => $userModel->orderBy('created_at', 'DESC')->limit(5)->find(),
            'recentOrders' => $orderModel->orderBy('created_at', 'DESC')->limit(5)->find()
        ];
        
        return view('admin/dashboard', $data);
    }
}