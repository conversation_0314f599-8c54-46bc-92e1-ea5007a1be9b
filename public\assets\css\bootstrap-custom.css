/* Bootstrap Custom CSS - Minimal styling using Bootstrap 5 classes */

/* General Styles */
body {
  font-family: 'Poppins', sans-serif;
  color: #333;
  line-height: 1.6;
}

.section-padding {
  padding: 80px 0;
}

.bg-light {
  background-color: #f8f9fa !important;
}

/* Navbar Styling */
.navbar {
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 10px 0;
  background-color: white;
}

.navbar-brand {
  font-weight: 700;
  font-size: 1.5rem;
  color: #007bff;
}

.navbar-brand i {
  color: #007bff;
}

.navbar .nav-link {
  font-weight: 500;
  padding: 10px 15px !important;
  color: #333;
}

.navbar .nav-link:hover {
  color: #007bff;
}

.navbar-nav .nav-item.active .nav-link {
  color: #007bff;
}

.navbar-scrolled {
  padding: 5px 0;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #007bff;
}

/* Hero Slider */
.hero-slider-container {
  position: relative;
  height: 450px;
  overflow: hidden;
}

.hero-slider, .hero-slide {
  height: 100%;
}

.hero-slide {
  position: relative;
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: center;
}

.hero-slide::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to right, rgba(0,0,0,0.7) 0%, rgba(0,0,0,0.4) 70%, rgba(0,0,0,0.1) 100%);
  z-index: 1;
}

.hero-content {
  position: relative;
  z-index: 2;
  color: white;
}

.hero-badge {
  display: inline-block;
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  padding: 6px 14px;
  border-radius: 30px;
  font-size: 0.85rem;
  font-weight: 600;
  margin-bottom: 1rem;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.hero-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: white;
}

.hero-subtitle {
  font-size: 1.1rem;
  margin-bottom: 1.5rem;
  color: rgba(255, 255, 255, 0.9);
}

.hero-buttons {
  margin-top: 1.5rem;
}

.hero-search {
  max-width: 500px;
  margin-top: 1.5rem;
}

.hero-search .form-control {
  height: 50px;
  border-radius: 4px 0 0 4px;
}

.hero-search .btn {
  border-radius: 0 4px 4px 0;
}

/* Swiper Navigation */
.swiper-button-next,
.swiper-button-prev {
  color: white;
  background: rgba(0, 123, 255, 0.7);
  width: 40px;
  height: 40px;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.swiper-button-next:hover,
.swiper-button-prev:hover {
  background: #007bff;
}

.swiper-button-next::after,
.swiper-button-prev::after {
  font-size: 1rem;
}

.swiper-pagination-bullet {
  background: white;
  opacity: 0.7;
}

.swiper-pagination-bullet-active {
  background: #007bff;
  opacity: 1;
}

/* Section Titles */
.section-title {
  margin-bottom: 3rem;
}

.section-subtitle {
  display: inline-block;
  font-size: 0.9rem;
  font-weight: 600;
  color: #007bff;
  margin-bottom: 0.75rem;
  text-transform: uppercase;
}

.section-title h2 {
  font-size: 2.2rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.section-divider {
  width: 60px;
  height: 3px;
  background-color: #007bff;
  margin: 1rem auto;
}

.section-description {
  max-width: 700px;
  margin: 0 auto;
  color: #6c757d;
}

/* Category Cards */
.premium-category-card {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  background: #fff;
  transition: all 0.3s ease;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  height: 100%;
  display: flex;
  flex-direction: column;
  margin-bottom: 20px;
}

.premium-category-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.category-image {
  height: 200px;
  position: relative;
}

.category-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.category-overlay {
  background: linear-gradient(180deg, transparent 40%, rgba(0,0,0,0.4) 100%);
  position: absolute;
  inset: 0;
  z-index: 1;
}

.category-icon {
  position: absolute;
  top: 15px;
  left: 15px;
  background: rgba(255, 255, 255, 0.8);
  padding: 8px;
  border-radius: 50%;
  font-size: 20px;
  color: #007bff;
  z-index: 2;
}

.category-details {
  padding: 20px;
  background: white;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.category-details h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 10px;
  color: #333;
}

.category-details p {
  font-size: 0.9rem;
  color: #6c757d;
  margin-bottom: 15px;
  flex-grow: 1;
}

.category-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
  border-top: 1px solid #eee;
  padding-top: 15px;
}

.paper-count {
  font-size: 0.85rem;
  color: #6c757d;
}

.paper-count i {
  color: #007bff;
  margin-right: 5px;
}

.explore-btn {
  font-size: 0.9rem;
  font-weight: 600;
  color: #007bff;
  text-decoration: none;
  display: flex;
  align-items: center;
}

.explore-btn i {
  margin-left: 5px;
  transition: all 0.3s ease;
}

.explore-btn:hover {
  color: #0056b3;
}

.explore-btn:hover i {
  transform: translateX(3px);
}

/* Feature Boxes */
.feature-box {
  text-align: center;
  padding: 30px 20px;
  border-radius: 8px;
  background-color: white;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  height: 100%;
}

.feature-box:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.feature-icon {
  width: 70px;
  height: 70px;
  margin: 0 auto 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 123, 255, 0.1);
  color: #007bff;
  border-radius: 50%;
  font-size: 1.5rem;
}

.feature-title {
  font-weight: 600;
  margin-bottom: 15px;
  color: #333;
}

.feature-text {
  color: #6c757d;
  font-size: 0.95rem;
}

/* Testimonials */
.testimonials-section {
  background-color: #f8f9fa;
}

.premium-testimonial-card {
  background-color: white;
  border-radius: 8px;
  padding: 25px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  position: relative;
  margin: 15px 10px;
  transition: all 0.3s ease;
  height: calc(100% - 30px);
}

.premium-testimonial-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.testimonial-quote-icon {
  position: absolute;
  top: 20px;
  right: 20px;
  color: #007bff;
  font-size: 1.2rem;
  opacity: 0.2;
}

.rating {
  margin-bottom: 15px;
  color: #ffc107;
}

.testimonial-content {
  font-style: italic;
  margin-bottom: 20px;
  color: #6c757d;
  font-size: 0.95rem;
  line-height: 1.6;
}

.testimonial-author {
  display: flex;
  align-items: center;
  border-top: 1px solid #eee;
  padding-top: 15px;
  margin-top: auto;
}

.testimonial-author img {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  margin-right: 15px;
  object-fit: cover;
  border: 2px solid #eee;
}

.testimonial-author-info h5 {
  margin-bottom: 3px;
  font-weight: 600;
  font-size: 1rem;
  color: #333;
}

.testimonial-author-info p {
  color: #007bff;
  margin: 0;
  font-size: 0.85rem;
}

.testimonial-slider-nav {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-top: 20px;
}

.testimonial-button-prev,
.testimonial-button-next {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
  color: #333;
}

.testimonial-button-prev:hover,
.testimonial-button-next:hover {
  background-color: #007bff;
  color: white;
}

/* Footer */
.premium-footer {
  background-color: #212529;
  color: rgba(255, 255, 255, 0.7);
  padding-top: 60px;
}

.footer-top {
  padding-bottom: 40px;
}

.footer-logo {
  display: flex;
  align-items: center;
  color: white;
  font-size: 1.5rem;
  font-weight: 700;
  text-decoration: none;
}

.footer-logo i {
  color: #007bff;
  font-size: 1.8rem;
  margin-right: 10px;
}

.footer-logo:hover {
  color: white;
}

.footer-social-icons {
  display: flex;
  margin-top: 20px;
  gap: 10px;
}

.social-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  color: white;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.social-icon:hover {
  background-color: #007bff;
  color: white;
}

.widget-title {
  color: white;
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 20px;
  position: relative;
  padding-bottom: 10px;
}

.widget-title::after {
  content: '';
  position: absolute;
  left: 0;
  bottom: 0;
  width: 40px;
  height: 2px;
  background: #007bff;
}

.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links li {
  margin-bottom: 10px;
}

.footer-links a {
  color: rgba(255, 255, 255, 0.7);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  text-decoration: none;
}

.footer-links a i {
  margin-right: 8px;
  font-size: 0.8rem;
  color: #007bff;
}

.footer-links a:hover {
  color: white;
  padding-left: 5px;
}

.footer-contact-info {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-contact-info li {
  display: flex;
  margin-bottom: 15px;
}

.footer-contact-info .icon {
  width: 30px;
  height: 30px;
  background-color: rgba(0, 123, 255, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  color: #007bff;
  flex-shrink: 0;
}

.footer-newsletter .form-control {
  background-color: rgba(255, 255, 255, 0.1);
  border: none;
  color: white;
  height: 45px;
}

.footer-newsletter .form-control::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.payment-methods {
  display: flex;
  gap: 10px;
}

.payment-methods span {
  font-size: 1.5rem;
  color: rgba(255, 255, 255, 0.5);
  transition: all 0.3s ease;
}

.payment-methods span:hover {
  color: white;
}

.footer-bottom {
  background-color: rgba(0, 0, 0, 0.2);
  padding: 20px 0;
}

.copyright {
  color: rgba(255, 255, 255, 0.7);
}

.footer-bottom-links {
  display: flex;
  justify-content: flex-end;
  gap: 20px;
}

.footer-bottom-links a {
  color: rgba(255, 255, 255, 0.7);
  transition: all 0.3s ease;
  font-size: 0.9rem;
  text-decoration: none;
}

.footer-bottom-links a:hover {
  color: white;
}

/* Paper Cards */
.paper-card {
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
}

.paper-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.paper-card .card-img {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.paper-card .card-img img {
  width: 100%;
  height: 200px;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.paper-card:hover .card-img img {
  transform: scale(1.05);
}

.paper-card .category-badge {
  position: absolute;
  top: 15px;
  right: 15px;
  background-color: rgba(0, 123, 255, 0.9);
  color: white;
  padding: 5px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  z-index: 2;
}

.paper-card .card-body {
  padding: 20px;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.paper-card .card-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 10px;
  color: #333;
  line-height: 1.4;
}

.paper-card .card-text {
  color: #6c757d;
  font-size: 0.9rem;
  margin-bottom: 15px;
  flex-grow: 1;
}

.paper-card .price-tag {
  font-weight: 700;
  color: #007bff;
  font-size: 1.2rem;
}

/* Responsive Adjustments */
@media (max-width: 991px) {
  .hero-title {
    font-size: 2rem;
  }

  .section-title h2 {
    font-size: 1.8rem;
  }
}

@media (max-width: 767px) {
  .hero-slider-container {
    height: 500px;
  }

  .hero-title {
    font-size: 1.8rem;
  }

  .section-padding {
    padding: 60px 0;
  }

  .footer-bottom-links {
    justify-content: center;
    margin-top: 15px;
  }

  .copyright {
    text-align: center;
  }
}
