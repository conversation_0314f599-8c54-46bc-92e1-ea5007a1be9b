<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;
use App\Models\PaperModel;

class UpdatePaperYears extends BaseCommand
{
    protected $group       = 'Database';
    protected $name        = 'update:paper-years';
    protected $description = 'Update paper years based on titles';

    public function run(array $params)
    {
        $paperModel = new PaperModel();

        // Get all papers
        $papers = $paperModel->findAll();

        CLI::write('Starting to update paper years...', 'green');

        foreach ($papers as $paper) {
            $year = null;
            
            // Extract year from title using regex
            if (preg_match('/\(\s*(\d{4})\s*\)/', $paper['title'], $matches)) {
                $year = (int)$matches[1];
            }
            
            if ($year && $year >= 1900 && $year <= 2100) {
                $paperModel->update($paper['id'], ['year' => $year]);
                CLI::write("Updated paper ID {$paper['id']} '{$paper['title']}' with year {$year}", 'yellow');
            } else {
                CLI::write("Could not extract year from: {$paper['title']}", 'red');
            }
        }

        CLI::write('Year update completed!', 'green');
    }
}
