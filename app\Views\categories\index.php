<?= $this->extend('templates/main_layout') ?>

<?= $this->section('content') ?>

<!-- Categories Header -->
<div class="bg-light py-4 border-bottom">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h1 class="h3 fw-bold mb-0">Browse Categories</h1>
            </div>
            <div class="col-md-6">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb mb-0 justify-content-md-end">
                        <li class="breadcrumb-item"><a href="<?= base_url() ?>" class="text-decoration-none">Home</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Categories</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>
</div>

<div class="container py-5">
    <!-- Categories Introduction -->
    <div class="card border-0 shadow-sm mb-5">
        <div class="card-body p-4">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h2 class="h4 fw-bold mb-3">Academic Paper Categories</h2>
                    <p class="mb-0">Explore our extensive collection of high-quality academic papers organized by categories. Each category contains papers carefully selected to help with your research and studies.</p>
                </div>
                <div class="col-md-4 text-md-end mt-3 mt-md-0">
                    <span class="badge bg-primary px-3 py-2">
                        <i class="fas fa-th-large me-1"></i> <?= count($categories) ?> Categories Available
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- Categories Listing -->
    <div class="row g-4">
        <?php if (empty($categories)): ?>
            <div class="col-12">
                <div class="card border-0 shadow-sm py-5">
                    <div class="card-body text-center py-5">
                        <div class="mb-4">
                            <img src="https://cdn-icons-png.flaticon.com/512/7486/7486754.png" alt="No Categories"
                                class="img-fluid mb-4" style="max-height: 150px; opacity: 0.7;">
                        </div>
                        <h3 class="h4 fw-bold mb-3">No Categories Available</h3>
                        <p class="text-muted mb-4 px-md-5 mx-md-5">There are no categories available at the moment. Please check back later.</p>
                        <a href="<?= base_url() ?>" class="btn btn-primary px-4 py-2">
                            <i class="fas fa-home me-2"></i>Return to Homepage
                        </a>
                    </div>
                </div>
            </div>
        <?php else: ?>
            <?php
            // Define icons for categories
            $icons = ['book', 'flask', 'calculator', 'language', 'chart-line', 'landmark', 'microscope', 'heartbeat', 'globe', 'laptop-code'];
            // Define background colors for variety
            $bgColors = ['primary', 'success', 'danger', 'warning', 'info'];

            foreach ($categories as $index => $category):
                $iconIndex = $index % count($icons);
                $icon = $icons[$iconIndex];
                $bgColorIndex = $index % count($bgColors);
                $bgColor = $bgColors[$bgColorIndex];
            ?>
                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="<?= 100 + (50 * $index) ?>">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="row g-0 h-100">
                            <!-- Category Image (Left Side) -->
                            <div class="col-md-5 position-relative" style="min-height: 150px;">
                                <?php if (!empty($category['preview_image'])): ?>
                                    <img src="<?= base_url($category['preview_image']) ?>"
                                        alt="<?= $category['name'] ?>"
                                        class="rounded-start"
                                        style="width: 150px; height: 150px; object-fit: cover; position: absolute; top: 0; left: 0;"
                                        loading="lazy">
                                <?php else: ?>
                                    <?php
                                    $imageIndex = ($index % 5) + 1;
                                    $placeholderImages = [
                                        'https://images.unsplash.com/photo-1532012197267-da84d127e765?ixlib=rb-4.0.3',
                                        'https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?ixlib=rb-4.0.3',
                                        'https://images.unsplash.com/photo-1513542789411-b6a5d4f31634?ixlib=rb-4.0.3',
                                        'https://images.unsplash.com/photo-1497633762265-9d179a990aa6?ixlib=rb-4.0.3',
                                        'https://images.unsplash.com/photo-1456513080510-7bf3a84b82f8?ixlib=rb-4.0.3'
                                    ];
                                    $placeholderImage = $placeholderImages[$imageIndex - 1] . '&auto=format&fit=crop&w=150&h=150&q=80';
                                    ?>
                                    <img src="<?= $placeholderImage ?>"
                                        alt="<?= $category['name'] ?>"
                                        class="rounded-start"
                                        style="width: 150px; height: 150px; object-fit: cover; position: absolute; top: 0; left: 0;"
                                        loading="lazy"
                                        onerror="this.src='<?= base_url('public/assets/images/default-category.jpg') ?>'; this.onerror=null;">
                                <?php endif; ?>

                                <!-- Category Icon -->
                                <div class="position-absolute top-0 start-0 m-2" style="z-index: 2;">
                                    <div class="d-flex align-items-center justify-content-center bg-white rounded-circle shadow-sm" style="width: 32px; height: 32px;">
                                        <i class="fas fa-<?= $icon ?> text-<?= $bgColor ?> small"></i>
                                    </div>
                                </div>
                            </div>

                            <!-- Content (Right Side) -->
                            <div class="col-md-7 d-flex flex-column">
                                <div class="card-body py-2 px-3 d-flex flex-column h-100">
                                    <h3 class="h6 fw-bold mb-1"><?= $category['name'] ?></h3>
                                    <p class="text-muted small mb-2 flex-grow-1" style="line-height: 1.4;"><?= substr($category['description'], 0, 80) . (strlen($category['description']) > 80 ? '...' : '') ?></p>

                                    <div class="d-flex justify-content-between align-items-center mt-auto">
                                        <span class="text-muted small">
                                            <i class="fas fa-file-alt text-<?= $bgColor ?> me-1"></i>
                                            <?php
                                            // Get paper count for this category
                                            $paperCount = isset($paperModel) ? count($paperModel->getPapersByCategory($category['id'])) : rand(5, 25);
                                            echo $paperCount . ' Papers';
                                            ?>
                                        </span>
                                        <a href="<?= base_url('categories/view/' . $category['id']) ?>" class="btn btn-sm btn-outline-<?= $bgColor ?> py-1 px-2">
                                            Explore <i class="fas fa-arrow-right ms-1"></i>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>
</div>

<?= $this->endSection() ?>