<?php

namespace App\Models;

use CodeIgniter\Model;

class UserModel extends Model
{
    protected $table = 'users';
    protected $primaryKey = 'id';
    protected $allowedFields = ['name', 'email', 'password', 'is_admin', 'status', 'created_at', 'updated_at', 'google_id', 'profile_image', 'oauth_provider'];
    protected $useTimestamps = false; // Set to false since we're manually handling timestamps
    protected $returnType = 'array';

    // Add this to enable detailed error reporting
    protected $skipValidation = false;

    public function getUserById($id)
    {
        return $this->find($id);
    }

    public function getUserByEmail($email)
    {
        return $this->where('email', $email)->first();
    }

    public function getUserByGoogleId($googleId)
    {
        return $this->where('google_id', $googleId)->first();
    }
}