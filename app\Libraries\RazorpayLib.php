<?php

namespace App\Libraries;

use Config\Razorpay as RazorpayConfig;

class RazorpayLib
{
    protected $config;

    public function __construct()
    {
        $this->config = new RazorpayConfig();
    }

    /**
     * Verify payment signature
     *
     * @param string $paymentId
     * @param string $orderId
     * @param string $signature
     * @return bool
     */
    public function verifyPaymentSignature($paymentId, $orderId, $signature)
    {
        $generatedSignature = hash_hmac('sha256', $orderId . '|' . $paymentId, $this->config->keySecret);
        
        return ($generatedSignature === $signature);
    }

    /**
     * Get payment details
     *
     * @param string $paymentId
     * @return array|null
     */
    public function getPaymentDetails($paymentId)
    {
        $url = $this->config->apiUrl . '/' . $this->config->apiVersion . '/payments/' . $paymentId;
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_USERPWD, $this->config->keyId . ':' . $this->config->keySecret);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
        
        $response = curl_exec($ch);
        $error = curl_error($ch);
        
        curl_close($ch);
        
        if ($error) {
            log_message('error', 'Razorpay API Error: ' . $error);
            return null;
        }
        
        return json_decode($response, true);
    }

    /**
     * Create an order
     *
     * @param float $amount
     * @param string $receipt
     * @param array $notes
     * @return array|null
     */
    public function createOrder($amount, $receipt, $notes = [])
    {
        $url = $this->config->apiUrl . '/' . $this->config->apiVersion . '/orders';
        
        $data = [
            'amount' => $amount * 100, // Convert to paise
            'currency' => 'INR',
            'receipt' => $receipt,
            'notes' => $notes
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_USERPWD, $this->config->keyId . ':' . $this->config->keySecret);
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
        
        $response = curl_exec($ch);
        $error = curl_error($ch);
        
        curl_close($ch);
        
        if ($error) {
            log_message('error', 'Razorpay API Error: ' . $error);
            return null;
        }
        
        return json_decode($response, true);
    }
}
