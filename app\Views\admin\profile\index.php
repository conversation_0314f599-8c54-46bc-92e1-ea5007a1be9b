<?= $this->extend('templates/admin_layout') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800"><?= $title ?></h1>
        <a href="<?= base_url('admin/profile/edit') ?>" class="d-none d-sm-inline-block btn btn-primary shadow-sm">
            <i class="fas fa-edit fa-sm text-white-50"></i> Edit Profile
        </a>
    </div>

    <?php if (session()->getFlashdata('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?= session()->getFlashdata('success') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <?php if (session()->getFlashdata('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?= session()->getFlashdata('error') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Admin Profile Information</h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-3 text-center mb-4">
                    <?php if (!empty($user['profile_image'])): ?>
                        <?php if (filter_var($user['profile_image'], FILTER_VALIDATE_URL)): ?>
                            <img src="<?= $user['profile_image'] ?>" alt="Profile" class="rounded-circle img-thumbnail" style="width: 150px; height: 150px; object-fit: cover;" referrerpolicy="no-referrer" onerror="this.onerror=null; this.src='<?= base_url('assets/images/default-avatar.svg') ?>'; this.style.width='150px'; this.style.height='150px';">
                        <?php else: ?>
                            <img src="<?= base_url($user['profile_image']) ?>" alt="Profile" class="rounded-circle img-thumbnail" style="width: 150px; height: 150px; object-fit: cover;" onerror="this.onerror=null; this.src='<?= base_url('assets/images/default-avatar.svg') ?>'; this.style.width='150px'; this.style.height='150px';">
                        <?php endif; ?>
                    <?php else: ?>
                        <img src="<?= base_url('assets/images/default-avatar.svg') ?>" alt="Profile" class="rounded-circle img-thumbnail" style="width: 150px; height: 150px;">
                    <?php endif; ?>
                </div>
                <div class="col-md-9">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <tr>
                                <th width="200">User ID</th>
                                <td><?= $user['id'] ?></td>
                            </tr>
                            <tr>
                                <th>Name</th>
                                <td><?= esc($user['name']) ?></td>
                            </tr>
                            <tr>
                                <th>Email</th>
                                <td><?= esc($user['email']) ?></td>
                            </tr>
                            <tr>
                                <th>Role</th>
                                <td><span class="badge bg-primary"><?= ucfirst($user['is_admin']) ?></span></td>
                            </tr>
                            <tr>
                                <th>Status</th>
                                <td>
                                    <?php if ($user['status'] === 'active'): ?>
                                        <span class="badge bg-success">Active</span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">Inactive</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <tr>
                                <th>Created At</th>
                                <td><?= $user['created_at'] ? date('F j, Y, g:i a', strtotime($user['created_at'])) : 'N/A' ?></td>
                            </tr>
                            <tr>
                                <th>Updated At</th>
                                <td><?= $user['updated_at'] ? date('F j, Y, g:i a', strtotime($user['updated_at'])) : 'N/A' ?></td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>
