<?= $this->extend('templates/main_layout') ?>

<?= $this->section('content') ?>

<style>
    .login-container {
        min-height: 100vh;
        padding: 1.5rem 0;
        background: linear-gradient(135deg, #e6f3ff 0%, #ffffff 100%);
    }

    .auth-card {
        background: rgba(255, 255, 255, 0.98);
        border-radius: 12px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
        padding: 1.5rem;
        transition: all 0.3s ease;
        max-width: 400px;
        margin: auto;
    }

    .auth-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 6px 18px rgba(33, 150, 243, 0.12);
    }

    .auth-title {
        color: #1976d2;
        font-weight: 600;
        font-size: 1.5rem;
        text-align: center;
        margin-bottom: 1.2rem;
    }

    .form-control {
        border-radius: 8px;
        padding: 0.6rem 0.8rem;
        border: 1px solid #e3f2fd;
        transition: all 0.3s ease;
        font-size: 0.95rem;
    }

    .form-control:focus {
        border-color: #2196f3;
        box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.1);
    }

    .btn-primary {
        background: #1976d2;
        border: none;
        border-radius: 8px;
        padding: 0.6rem;
        font-weight: 500;
        transition: all 0.3s ease;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        font-size: 0.9rem;
    }

    .btn-primary:hover {
        background: #1565c0;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(25, 118, 210, 0.2);
    }

    .social-login {
        background: #fff;
        border: 1px solid #e3f2fd;
        border-radius: 8px;
        transition: all 0.3s ease;
        padding: 0.6rem;
        font-size: 0.9rem;
    }

    .social-login:hover {
        background: #f5f5f5;
        transform: translateY(-2px);
        border-color: #bbdefb;
    }

    .auth-links {
        margin-top: 1rem;
    }

    .auth-links a {
        color: #1976d2;
        text-decoration: none;
        transition: color 0.3s ease;
        font-size: 0.9rem;
    }

    .auth-links a:hover {
        color: #1565c0;
    }

    .form-label {
        font-size: 0.9rem;
        font-weight: 500;
        color: #455a64;
    }

    .form-check-label {
        font-size: 0.9rem;
        color: #546e7a;
    }

    .alert {
        border-radius: 8px;
        font-size: 0.9rem;
        padding: 0.75rem 1rem;
    }
</style>

<div class="login-container d-flex align-items-center justify-content-center">
    <div class="row w-100 justify-content-center">
        <div class="col-md-4 col-sm-10">
            <div class="auth-card">
                <h2 class="auth-title">Login </h2>

                <?php if (session()->getFlashdata('success')): ?>
                    <div class="alert alert-success">
                        <?= session()->getFlashdata('success') ?>
                    </div>
                <?php endif; ?>

                <?php if (session()->getFlashdata('error')): ?>
                    <div class="alert alert-danger">
                        <?= session()->getFlashdata('error') ?>
                    </div>
                <?php endif; ?>

                <form action="<?= base_url('auth/login') ?>" method="post">
                    <?= csrf_field() ?>

                    <div class="mb-3">
                        <label for="email" class="form-label">Email Address</label>
                        <input type="email" class="form-control <?= (isset(session('errors')['email'])) ? 'is-invalid' : '' ?>" id="email" name="email" value="<?= old('email') ?>" required>
                        <?php if (isset(session('errors')['email'])): ?>
                            <div class="invalid-feedback">
                                <?= session('errors')['email'] ?>
                            </div>
                        <?php endif; ?>
                    </div>

                    <div class="mb-3">
                        <label for="password" class="form-label">Password</label>
                        <input type="password" class="form-control <?= (isset(session('errors')['password'])) ? 'is-invalid' : '' ?>" id="password" name="password" required>
                        <?php if (isset(session('errors')['password'])): ?>
                            <div class="invalid-feedback">
                                <?= session('errors')['password'] ?>
                            </div>
                        <?php endif; ?>
                    </div>

                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="remember" name="remember">
                        <label class="form-check-label" for="remember">Remember me</label>
                    </div>

                    <div class="d-grid gap-2 mb-4">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-sign-in-alt me-2"></i>Login
                        </button>
                    </div>

                    <div class="text-center mb-4">
                        <p class="text-muted">Or sign in with</p>
                        <a href="<?= $googleLoginUrl ?>" class="btn social-login w-100">
                            <i class="fab fa-google me-2 text-danger"></i> Continue with Google
                        </a>
                    </div>

                    <div class="auth-links text-center">
                        <p class="mb-2">
                            <a href="<?= base_url('auth/forgot-password') ?>">
                                <i class="fas fa-key me-1"></i> Forgot Password?
                            </a>
                        </p>
                        <p class="mb-0">
                            Don't have an account?
                            <a href="<?= base_url('auth/register') ?>" class="fw-bold">
                                Create Account
                            </a>
                        </p>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>