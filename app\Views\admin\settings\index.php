<?= $this->extend('templates/admin_layout') ?>

<?= $this->section('content') ?>
<div class="container-fluid px-4">
    <h1 class="mt-4"><?= $title ?></h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="<?= base_url('admin/dashboard') ?>">Dashboard</a></li>
        <li class="breadcrumb-item active">Settings</li>
    </ol>
    
    <?php if (session()->getFlashdata('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?= session()->getFlashdata('success') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>
    
    <?php if (session()->getFlashdata('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?= session()->getFlashdata('error') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>
    
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-cog me-1"></i>
            Site Settings
        </div>
        <div class="card-body">
            <form action="<?= base_url('admin/settings/update') ?>" method="post">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <h5>General Settings</h5>
                        <div class="mb-3">
                            <label for="site_name" class="form-label">Site Name</label>
                            <input type="text" class="form-control" id="site_name" name="site_name" value="<?= esc($settings['site_name']) ?>" required>
                        </div>
                        <div class="mb-3">
                            <label for="site_description" class="form-label">Site Description</label>
                            <textarea class="form-control" id="site_description" name="site_description" rows="3" required><?= esc($settings['site_description']) ?></textarea>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h5>Contact Information</h5>
                        <div class="mb-3">
                            <label for="contact_email" class="form-label">Contact Email</label>
                            <input type="email" class="form-control" id="contact_email" name="contact_email" value="<?= esc($settings['contact_email']) ?>" required>
                        </div>
                        <div class="mb-3">
                            <label for="contact_phone" class="form-label">Contact Phone</label>
                            <input type="text" class="form-control" id="contact_phone" name="contact_phone" value="<?= esc($settings['contact_phone']) ?>" required>
                        </div>
                        <div class="mb-3">
                            <label for="address" class="form-label">Address</label>
                            <textarea class="form-control" id="address" name="address" rows="3" required><?= esc($settings['address']) ?></textarea>
                        </div>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-md-6">
                        <h5>Payment Settings</h5>
                        <div class="mb-3">
                            <label for="currency" class="form-label">Currency</label>
                            <select class="form-select" id="currency" name="currency" required>
                                <option value="INR" <?= $settings['currency'] == 'INR' ? 'selected' : '' ?>>Indian Rupee (₹)</option>
                                <option value="USD" <?= $settings['currency'] == 'USD' ? 'selected' : '' ?>>US Dollar ($)</option>
                                <option value="EUR" <?= $settings['currency'] == 'EUR' ? 'selected' : '' ?>>Euro (€)</option>
                                <option value="GBP" <?= $settings['currency'] == 'GBP' ? 'selected' : '' ?>>British Pound (£)</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="tax_rate" class="form-label">Tax Rate (%)</label>
                            <input type="number" class="form-control" id="tax_rate" name="tax_rate" value="<?= esc($settings['tax_rate']) ?>" min="0" step="0.01" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h5>Site Options</h5>
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="enable_registration" name="enable_registration" value="1" <?= $settings['enable_registration'] ? 'checked' : '' ?>>
                            <label class="form-check-label" for="enable_registration">Enable User Registration</label>
                        </div>
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="enable_guest_checkout" name="enable_guest_checkout" value="1" <?= $settings['enable_guest_checkout'] ? 'checked' : '' ?>>
                            <label class="form-check-label" for="enable_guest_checkout">Enable Guest Checkout</label>
                        </div>
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="maintenance_mode" name="maintenance_mode" value="1" <?= $settings['maintenance_mode'] ? 'checked' : '' ?>>
                            <label class="form-check-label" for="maintenance_mode">Maintenance Mode</label>
                        </div>
                    </div>
                </div>
                
                <div class="text-end">
                    <button type="submit" class="btn btn-primary">Save Settings</button>
                </div>
            </form>
        </div>
    </div>
</div>
<?= $this->endSection() ?>
