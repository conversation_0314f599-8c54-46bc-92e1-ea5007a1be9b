/* Checkout Progress Bar */
.checkout-progress {
    display: flex;
    align-items: center;
    width: 100%;
    max-width: 600px;
}

.checkout-progress-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    z-index: 1;
}

.checkout-progress-step-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #f8f9fa;
    border: 2px solid #dee2e6;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6c757d;
    transition: all 0.3s ease;
}

.checkout-progress-step.active .checkout-progress-step-icon {
    background-color: #007bff;
    border-color: #007bff;
    color: white;
}

.checkout-progress-step-text {
    margin-top: 8px;
    font-size: 0.85rem;
    font-weight: 500;
    color: #6c757d;
}

.checkout-progress-step.active .checkout-progress-step-text {
    color: #007bff;
    font-weight: 600;
}

.checkout-progress-connector {
    flex-grow: 1;
    height: 2px;
    background-color: #dee2e6;
    margin: 0 10px;
    position: relative;
    top: -20px;
    z-index: 0;
}

.checkout-progress-connector.active {
    background-color: #007bff;
}

/* Order Summary */
.order-summary {
    position: sticky;
    top: 2rem;
}

.order-summary-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;
}

.order-summary-total {
    display: flex;
    justify-content: space-between;
    font-weight: 700;
    font-size: 1.1rem;
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px solid #dee2e6;
}

/* Form Styling */
.form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-label {
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.form-check-label {
    font-weight: normal;
}

.payment-method-card {
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
    padding: 1rem;
    margin-bottom: 1rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.payment-method-card:hover {
    border-color: #007bff;
    background-color: rgba(0, 123, 255, 0.05);
}

.payment-method-card.active {
    border-color: #007bff;
    background-color: rgba(0, 123, 255, 0.05);
}

.payment-method-card-header {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
}

.payment-method-card-radio {
    margin-right: 0.5rem;
}

.payment-method-card-title {
    font-weight: 600;
    margin-bottom: 0;
}

.payment-method-card-body {
    padding-left: 1.5rem;
}

/* Order Item */
.order-item {
    display: flex;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #f0f0f0;
}

.order-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.order-item-image {
    width: 60px;
    height: 60px;
    border-radius: 4px;
    overflow: hidden;
    margin-right: 1rem;
    flex-shrink: 0;
}

.order-item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.order-item-details {
    flex-grow: 1;
}

.order-item-title {
    font-weight: 600;
    margin-bottom: 0.25rem;
    font-size: 0.95rem;
}

.order-item-meta {
    color: #6c757d;
    font-size: 0.85rem;
}

.order-item-price {
    font-weight: 600;
    color: #007bff;
    text-align: right;
    flex-shrink: 0;
    margin-left: 1rem;
}

/* Trust Badges */
.trust-badge {
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;
}

.trust-badge-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: rgba(0, 123, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.75rem;
    color: #007bff;
    flex-shrink: 0;
}

.trust-badge-text {
    font-size: 0.85rem;
    color: #495057;
}

/* Responsive Adjustments */
@media (max-width: 767.98px) {
    .order-summary {
        position: static;
        margin-top: 2rem;
    }
}
