<?= $this->extend('templates/main_layout') ?>

<?= $this->section('content') ?>
<!-- <PERSON><PERSON>er -->
<div class="bg-light py-4 border-bottom">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h1 class="h3 fw-bold mb-0">Your Shopping Cart</h1>
            </div>
            <div class="col-md-6">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb mb-0 justify-content-md-end">
                        <li class="breadcrumb-item"><a href="<?= base_url() ?>" class="text-decoration-none">Home</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Cart</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>
</div>

<!-- Cart Content -->
<section class="py-5">
    <div class="container">
        <?php if (empty($items)): ?>
            <div class="row justify-content-center">
                <div class="col-md-8 text-center">
                    <div class="card border-0 shadow-sm p-5">
                        <div class="mb-4">
                            <img src="https://cdn-icons-png.flaticon.com/512/2038/2038854.png"
                                alt="Empty Cart" class="img-fluid mb-3"
                                style="max-height: 150px; opacity: 0.7;">
                        </div>
                        <h3 class="h4 fw-bold mb-3">Your Cart is Empty</h3>
                        <p class="text-muted mb-4 px-md-5">Looks like you haven't added any papers to your cart yet. Browse our collection to find the perfect academic resources for your needs.</p>
                        <div class="d-grid gap-2 col-md-6 mx-auto">
                            <a href="<?= base_url('papers') ?>" class="btn btn-primary btn-lg">
                                <i class="fas fa-shopping-bag me-2"></i>Browse Papers
                            </a>
                            <a href="<?= base_url() ?>" class="btn btn-outline-secondary">
                                <i class="fas fa-home me-2"></i>Return to Homepage
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        <?php else: ?>
            <div class="row g-4">
                <!-- Cart Items -->
                <div class="col-lg-8">
                    <div class="card border-0 shadow-sm mb-4">
                        <div class="card-header bg-white py-3 d-flex justify-content-between align-items-center">
                            <h5 class="mb-0 fw-bold">
                                <i class="fas fa-shopping-cart text-primary me-2"></i>Cart Items (<?= count($items) ?>)
                            </h5>
                            <a href="<?= base_url('cart/clear') ?>" class="btn btn-sm btn-outline-danger" onclick="return confirm('Are you sure you want to clear your cart?');">
                                <i class="fas fa-trash-alt me-1"></i>Clear Cart
                            </a>
                        </div>
                        <div class="card-body p-0">
                            <ul class="list-group list-group-flush">
                                <?php foreach ($items as $item): ?>
                                    <li class="list-group-item p-4 border-bottom">
                                        <div class="row align-items-center">
                                            <!-- Product Image -->
                                            <div class="col-md-3 col-sm-4 mb-3 mb-md-0 text-center">
                                                <?php if (!empty($item['preview_path'])): ?>
                                                    <img src="<?= base_url('uploads/papers/previews/' . $item['preview_path']) ?>"
                                                        alt="<?= esc($item['title']) ?>"
                                                        class="img-fluid rounded shadow-sm"
                                                        style="width: 100px; height: 100px; object-fit: cover;">
                                                <?php else: ?>
                                                    <div class="bg-light rounded shadow-sm d-flex align-items-center justify-content-center mx-auto" style="width: 100px; height: 100px;">
                                                        <i class="fas fa-file-alt text-primary fa-2x"></i>
                                                    </div>
                                                <?php endif; ?>
                                            </div>

                                            <!-- Product Details -->
                                            <div class="col-md-6 col-sm-8 mb-3 mb-md-0">
                                                <h5 class="fw-bold mb-2"><?= esc($item['title']) ?></h5>
                                                <div class="d-flex align-items-center mb-2">
                                                    <span class="badge bg-primary bg-opacity-10 text-primary me-2">Digital Download</span>
                                                    <?php if (isset($item['category_name'])): ?>
                                                        <span class="badge bg-secondary bg-opacity-10 text-secondary"><?= $item['category_name'] ?></span>
                                                    <?php endif; ?>
                                                </div>
                                                <p class="text-muted small mb-0">
                                                    <i class="fas fa-file-download me-1 text-primary"></i> Instant download after purchase
                                                </p>
                                            </div>

                                            <!-- Price & Actions -->
                                            <div class="col-md-3 text-md-end text-center mt-3 mt-md-0">
                                                <h5 class="text-primary fw-bold mb-3">$<?= number_format($item['price'], 2) ?></h5>
                                                <a href="<?= base_url('cart/remove/' . $item['id']) ?>" class="btn btn-sm btn-outline-danger" onclick="return confirm('Are you sure you want to remove this item?');">
                                                    <i class="fas fa-trash-alt me-1"></i>Remove
                                                </a>
                                            </div>
                                        </div>
                                    </li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                        <div class="card-footer bg-white py-3">
                            <a href="<?= base_url('papers') ?>" class="btn btn-outline-primary">
                                <i class="fas fa-arrow-left me-2"></i>Continue Shopping
                            </a>
                        </div>
                    </div>

                    <!-- Recommended Papers -->

                </div>

                <!-- Order Summary -->
                <div class="col-lg-4">
                    <div class="card border-0 shadow-sm mb-4 sticky-lg-top" style="top: 2rem; z-index: 100;">
                        <div class="card-header bg-white py-3">
                            <h5 class="mb-0 fw-bold">
                                <i class="fas fa-receipt text-primary me-2"></i>Order Summary
                            </h5>
                        </div>
                        <div class="card-body p-4">
                            <div class="d-flex justify-content-between mb-3">
                                <span class="text-muted">Subtotal</span>
                                <span class="fw-bold">$<?= number_format($total, 2) ?></span>
                            </div>

                            <?php
                            $tax = $total * 0.08; // 8% tax rate
                            $finalTotal = $total + $tax;
                            ?>

                            <div class="d-flex justify-content-between mb-3">
                                <span class="text-muted">Tax (8%)</span>
                                <span>$<?= number_format($tax, 2) ?></span>
                            </div>

                            <hr class="my-3">

                            <div class="d-flex justify-content-between mb-4">
                                <span class="fw-bold">Total</span>
                                <span class="fw-bold fs-4 text-primary">$<?= number_format($finalTotal, 2) ?></span>
                            </div>

                            <div class="mb-4">
                                <div class="input-group">
                                    <input type="text" class="form-control" placeholder="Promo code">
                                    <button class="btn btn-outline-primary" type="button">Apply</button>
                                </div>
                                <div class="form-text small text-muted mt-2">
                                    Enter promo code if you have one
                                </div>
                            </div>

                            <div class="bg-light p-3 rounded mb-4">
                                <div class="d-flex align-items-center mb-3">
                                    <div class="d-flex align-items-center justify-content-center bg-primary bg-opacity-10 rounded-circle me-3" style="width: 36px; height: 36px;">
                                        <i class="fas fa-shield-alt text-primary"></i>
                                    </div>
                                    <span class="small fw-medium">Secure Checkout</span>
                                </div>
                                <div class="d-flex align-items-center mb-3">
                                    <div class="d-flex align-items-center justify-content-center bg-primary bg-opacity-10 rounded-circle me-3" style="width: 36px; height: 36px;">
                                        <i class="fas fa-download text-primary"></i>
                                    </div>
                                    <span class="small fw-medium">Instant Download</span>
                                </div>
                                <div class="d-flex align-items-center">
                                    <div class="d-flex align-items-center justify-content-center bg-primary bg-opacity-10 rounded-circle me-3" style="width: 36px; height: 36px;">
                                        <i class="fas fa-headset text-primary"></i>
                                    </div>
                                    <span class="small fw-medium">24/7 Customer Support</span>
                                </div>
                            </div>

                            <div class="d-grid gap-2">
                                <a href="<?= base_url('cart/checkout') ?>" class="btn btn-primary btn-lg">
                                    <i class="fas fa-credit-card me-2"></i>Proceed to Checkout
                                </a>
                                <div class="text-center mt-2">
                                    <span class="badge bg-success px-3 py-2">
                                        <i class="fas fa-bolt me-1"></i> One-Page Checkout
                                    </span>
                                </div>
                            </div>

                            <div class="mt-4 pt-4 border-top text-center">
                                <h6 class="mb-3 small text-uppercase text-muted">We Accept</h6>
                                <div class="d-flex justify-content-center gap-3">
                                    <i class="fab fa-cc-visa fa-2x text-primary"></i>
                                    <i class="fab fa-cc-mastercard fa-2x text-primary"></i>
                                    <i class="fab fa-cc-amex fa-2x text-primary"></i>
                                    <i class="fab fa-cc-paypal fa-2x text-primary"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
</section>
<?= $this->endSection() ?>