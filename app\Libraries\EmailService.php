<?php

namespace App\Libraries;

use CodeIgniter\Email\Email;

class EmailService
{
    protected $email;

    public function __construct()
    {
        $this->email = \Config\Services::email();
    }

    /**
     * Send a password reset email
     *
     * @param string $to Recipient email
     * @param string $name Recipient name
     * @param string $token Reset token
     * @return bool
     */
    public function sendPasswordResetEmail($to, $name, $token)
    {
        $resetLink = base_url('auth/reset-password/' . $token);

        $subject = 'Reset Your Password - Sample Paper Store';

        $message = '
        <html>
        <head>
            <title>Reset Your Password</title>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background-color: #4a6ee0; color: white; padding: 10px 20px; text-align: center; }
                .content { padding: 20px; border: 1px solid #ddd; }
                .button { display: inline-block; background-color: #4a6ee0; color: white; text-decoration: none; padding: 10px 20px; border-radius: 5px; margin: 20px 0; }
                .footer { font-size: 12px; color: #777; margin-top: 20px; text-align: center; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>Reset Your Password</h1>
                </div>
                <div class="content">
                    <p>Hello ' . $name . ',</p>
                    <p>We received a request to reset your password for your Sample Paper Store account. Click the button below to reset your password:</p>
                    <p style="text-align: center;">
                        <a href="' . $resetLink . '" class="button">Reset Password</a>
                    </p>
                    <p>If you did not request a password reset, please ignore this email or contact us if you have questions.</p>
                    <p>This password reset link is only valid for 1 hour.</p>
                    <p>If the button above doesn\'t work, copy and paste the following URL into your browser:</p>
                    <p>' . $resetLink . '</p>
                </div>
                <div class="footer">
                    <p>&copy; ' . date('Y') . ' Sample Paper Store. All rights reserved.</p>
                </div>
            </div>
        </body>
        </html>
        ';

        // For development environment, just log the email and return true
        if (ENVIRONMENT === 'development') {
            // Log the email content for debugging
            log_message('info', 'Password reset email would be sent to: ' . $to);
            log_message('info', 'Reset link: ' . $resetLink);

            // Save the email to a file for viewing
            $emailFile = WRITEPATH . 'logs/reset_email_' . time() . '.html';
            file_put_contents($emailFile, $message);

            return true;
        }

        // For production, try to send the actual email
        $this->email->setFrom('<EMAIL>', 'Sample Paper Store');
        $this->email->setTo($to);
        $this->email->setSubject($subject);
        $this->email->setMessage($message);

        if ($this->email->send()) {
            return true;
        } else {
            // Log the error for debugging
            log_message('error', 'Failed to send password reset email: ' . $this->email->printDebugger(['headers']));
            return false;
        }
    }
}
