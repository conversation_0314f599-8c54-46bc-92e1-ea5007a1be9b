<?php

namespace App\Models;

use CodeIgniter\Model;

class PasswordResetAttemptModel extends Model
{
    protected $table = 'password_reset_attempts';
    protected $primaryKey = 'id';
    protected $allowedFields = ['ip_address', 'email', 'created_at'];
    protected $useTimestamps = false;
    protected $returnType = 'array';

    /**
     * Count attempts from an IP address within a time period
     *
     * @param string $ipAddress
     * @param int $minutes Time period in minutes
     * @return int
     */
    public function countAttemptsByIp($ipAddress, $minutes = 60)
    {
        $timeLimit = date('Y-m-d H:i:s', time() - ($minutes * 60));
        
        return $this->where('ip_address', $ipAddress)
                    ->where('created_at >', $timeLimit)
                    ->countAllResults();
    }

    /**
     * Count attempts for an email within a time period
     *
     * @param string $email
     * @param int $minutes Time period in minutes
     * @return int
     */
    public function countAttemptsByEmail($email, $minutes = 60)
    {
        $timeLimit = date('Y-m-d H:i:s', time() - ($minutes * 60));
        
        return $this->where('email', $email)
                    ->where('created_at >', $timeLimit)
                    ->countAllResults();
    }

    /**
     * Record a new password reset attempt
     *
     * @param string $ipAddress
     * @param string $email
     * @return bool
     */
    public function recordAttempt($ipAddress, $email)
    {
        return $this->insert([
            'ip_address' => $ipAddress,
            'email' => $email,
            'created_at' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * Clean up old attempts
     *
     * @param int $hours Time in hours
     * @return bool
     */
    public function cleanOldAttempts($hours = 24)
    {
        $timeLimit = date('Y-m-d H:i:s', time() - ($hours * 3600));
        
        return $this->where('created_at <', $timeLimit)->delete();
    }
}
