<?= $this->extend('templates/admin_layout') ?>

<?= $this->section('content') ?>
<div class="container-fluid px-4">
    <h1 class="mt-4"><?= $title ?></h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="/admin">Dashboard</a></li>
        <li class="breadcrumb-item"><a href="/admin/categories">Categories</a></li>
        <li class="breadcrumb-item active">Create</li>
    </ol>

    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-plus me-1"></i>
            Create New Category
        </div>
        <div class="card-body">
            <form action="<?=base_url('/admin/categories/create');?>" method="post" enctype="multipart/form-data">
                <?= csrf_field() ?>
                <div class="mb-3">
                    <label for="name" class="form-label">Name</label>
                    <input type="text" class="form-control <?= (isset($validation) && $validation->hasError('name')) ? 'is-invalid' : '' ?>" 
                           id="name" name="name" value="<?= old('name') ?>" required>
                    <?php if (isset($validation) && $validation->hasError('name')) : ?>
                        <div class="invalid-feedback">
                            <?= $validation->getError('name') ?>
                        </div>
                    <?php endif; ?>
                </div>

                <div class="mb-3">
                    <label for="description" class="form-label">Description</label>
                    <textarea class="form-control <?= (isset($validation) && $validation->hasError('description')) ? 'is-invalid' : '' ?>" 
                              id="description" name="description" rows="3" required><?= old('description') ?></textarea>
                    <?php if (isset($validation) && $validation->hasError('description')) : ?>
                        <div class="invalid-feedback">
                            <?= $validation->getError('description') ?>
                        </div>
                    <?php endif; ?>
                </div>

                <div class="mb-3">
                    <label for="preview_image" class="form-label">Preview Image</label>
                    <input type="file" class="form-control <?= (isset($validation) && $validation->hasError('preview_image')) ? 'is-invalid' : '' ?>" 
                           id="preview_image" name="preview_image" accept="image/*" required>
                    <?php if (isset($validation) && $validation->hasError('preview_image')) : ?>
                        <div class="invalid-feedback">
                            <?= $validation->getError('preview_image') ?>
                        </div>
                    <?php endif; ?>
                </div>

                <div class="mb-3">
                    <img id="imagePreview" src="#" alt="Preview" style="max-width: 200px; display: none;">
                </div>

                <button type="submit" class="btn btn-primary">Create Category</button>
                <a href="/admin/categories" class="btn btn-secondary">Cancel</a>
            </form>
        </div>
    </div>
</div>

<?= $this->section('scripts') ?>
<script>
    // Preview image before upload
    document.getElementById('preview_image').onchange = function(evt) {
        const [file] = this.files;
        if (file) {
            const preview = document.getElementById('imagePreview');
            preview.src = URL.createObjectURL(file);
            preview.style.display = 'block';
        }
    };
</script>
<?= $this->endSection() ?>

<?= $this->endSection() ?>