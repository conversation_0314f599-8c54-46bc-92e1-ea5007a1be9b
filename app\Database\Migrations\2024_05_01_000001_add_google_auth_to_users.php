<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AddGoogleAuthToUsers extends Migration
{
    public function up()
    {
        $this->forge->addColumn('users', [
            'google_id' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'null' => true,
                'after' => 'password'
            ],
            'oauth_provider' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
                'null' => true,
                'after' => 'google_id'
            ],
            'profile_image' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'null' => true,
                'after' => 'oauth_provider'
            ]
        ]);
    }

    public function down()
    {
        $this->forge->dropColumn('users', 'google_id');
        $this->forge->dropColumn('users', 'oauth_provider');
        $this->forge->dropColumn('users', 'profile_image');
    }
}
