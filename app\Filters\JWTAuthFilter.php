<?php

namespace App\Filters;

use CodeIgniter\Filters\FilterInterface;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;
use Config\Services;

class JW<PERSON>uthFilter implements FilterInterface
{
    /**
     * Filter requests to verify JWT token
     *
     * @param RequestInterface $request
     * @param array|null $arguments
     * @return mixed
     */
    public function before(RequestInterface $request, $arguments = null)
    {
        // Load the JWT helper
        helper('jwt');

        // Get token from header
        $token = getJWTFromHeader();

        // If no token found, return 401 Unauthorized
        if (!$token) {
            return Services::response()
                ->setStatusCode(ResponseInterface::HTTP_UNAUTHORIZED)
                ->setJSON([
                    'status' => false,
                    'message' => 'Access denied. No token provided.'
                ]);
        }

        // Validate token
        $userData = getUserFromJWTToken($token);

        // If token is invalid, return 401 Unauthorized
        if (!$userData) {
            return Services::response()
                ->setStatusCode(ResponseInterface::HTTP_UNAUTHORIZED)
                ->setJSON([
                    'status' => false,
                    'message' => 'Access denied. Invalid token.'
                ]);
        }

        // If user is admin and admin-only route, check admin status
        if (in_array('admin-only', $arguments) && $userData['is_admin'] !== 'admin') {
            return Services::response()
                ->setStatusCode(ResponseInterface::HTTP_FORBIDDEN)
                ->setJSON([
                    'status' => false,
                    'message' => 'Access denied. Admin privileges required.'
                ]);
        }

        // Store user data in a way that controllers can access
        $request->jwtUser = $userData;
        return $request;
    }

    /**
     * We don't have anything to do here
     *
     * @param RequestInterface $request
     * @param ResponseInterface $response
     * @param array|null $arguments
     * @return mixed
     */
    public function after(RequestInterface $request, ResponseInterface $response, $arguments = null)
    {
        // Do nothing
    }
}
