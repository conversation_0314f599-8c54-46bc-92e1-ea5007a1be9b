<?= $this->extend('templates/admin_layout') ?>

<?= $this->section('content') ?>
<div class="container-fluid px-4">
    <h1 class="mt-4"><?= $title ?></h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="<?= base_url('admin/dashboard') ?>">Dashboard</a></li>
        <li class="breadcrumb-item active">User Reports</li>
    </ol>
    
    <div class="row">
        <div class="col-xl-4 col-md-6">
            <div class="card bg-primary text-white mb-4">
                <div class="card-body">
                    <h2 class="mb-0"><?= $totalUsers ?></h2>
                    <div>Total Users</div>
                </div>
            </div>
        </div>
        <div class="col-xl-4 col-md-6">
            <div class="card bg-success text-white mb-4">
                <div class="card-body">
                    <h2 class="mb-0"><?= $activeUsers ?></h2>
                    <div>Active Users</div>
                </div>
            </div>
        </div>
        <div class="col-xl-4 col-md-6">
            <div class="card bg-info text-white mb-4">
                <div class="card-body">
                    <h2 class="mb-0"><?= $adminUsers ?></h2>
                    <div>Admin Users</div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-xl-12">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-chart-bar me-1"></i>
                    User Registrations Last 30 Days
                </div>
                <div class="card-body">
                    <canvas id="userRegistrationsChart" width="100%" height="40"></canvas>
                </div>
            </div>
        </div>
    </div>
    
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-table me-1"></i>
            Top Purchasing Users
        </div>
        <div class="card-body">
            <table id="topUsersTable" class="table table-striped">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Name</th>
                        <th>Email</th>
                        <th>Orders</th>
                        <th>Total Spent</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($topUsers as $user): ?>
                        <tr>
                            <td><?= $user['id'] ?></td>
                            <td><?= esc($user['name']) ?></td>
                            <td><?= esc($user['email']) ?></td>
                            <td><?= $user['order_count'] ?></td>
                            <td><?= number_format($user['total_spent'], 2) ?></td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
    
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-table me-1"></i>
            Daily Registration Data
        </div>
        <div class="card-body">
            <table id="userDataTable" class="table table-striped">
                <thead>
                    <tr>
                        <th>Date</th>
                        <th>New Users</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($userData as $data): ?>
                        <tr>
                            <td><?= date('M d, Y', strtotime($data['reg_date'])) ?></td>
                            <td><?= $data['user_count'] ?></td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // User Registrations Chart
        const userData = <?= json_encode($userData) ?>;
        const dates = userData.map(item => item.reg_date).reverse();
        const counts = userData.map(item => item.user_count).reverse();
        
        const userCtx = document.getElementById('userRegistrationsChart').getContext('2d');
        new Chart(userCtx, {
            type: 'bar',
            data: {
                labels: dates,
                datasets: [{
                    label: 'New Users',
                    data: counts,
                    backgroundColor: 'rgba(0, 123, 255, 0.5)',
                    borderColor: 'rgba(0, 123, 255, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                }
            }
        });
        
        // Initialize DataTables
        $('#topUsersTable').DataTable({
            paging: true,
            searching: true,
            ordering: true,
            info: true
        });
        
        $('#userDataTable').DataTable({
            paging: true,
            searching: true,
            ordering: true,
            info: true
        });
    });
</script>
<?= $this->endSection() ?>
