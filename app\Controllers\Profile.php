<?php

namespace App\Controllers;

use App\Models\UserModel;
use App\Models\OrderModel;
use App\Models\OrderItemModel;

class Profile extends BaseController
{
    protected $userModel;
    protected $orderModel;
    protected $orderItemModel;

    public function __construct()
    {
        $this->userModel = new UserModel();
        $this->orderModel = new OrderModel();
        $this->orderItemModel = new OrderItemModel();
    }

    public function index()
    {
        // Check if user is logged in
        if (!session()->get('isLoggedIn')) {
            return redirect()->to('/auth/login')->with('error', 'Please login to view your profile.');
        }

        $userId = session()->get('id');
        $user = $this->userModel->find($userId);

        if (!$user) {
            return redirect()->to('/auth/logout')->with('error', 'User not found.');
        }

        $data = [
            'title' => 'My Profile',
            'user' => $user
        ];

        return view('profile/index', $data);
    }

    public function edit()
    {
        // Check if user is logged in
        if (!session()->get('isLoggedIn')) {
            return redirect()->to('/auth/login')->with('error', 'Please login to edit your profile.');
        }

        $userId = session()->get('id');
        $user = $this->userModel->find($userId);

        if (!$user) {
            return redirect()->to('/auth/logout')->with('error', 'User not found.');
        }

        if ($this->request->getMethod() === 'post') {
            $rules = [
                'name' => 'required|min_length[3]|max_length[50]',
                'email' => 'required|valid_email'
            ];

            // Check if email is changed
            if ($user['email'] !== $this->request->getPost('email')) {
                $rules['email'] .= '|is_unique[users.email]';
            }

            // Check if password is being updated
            if ($this->request->getPost('password')) {
                $rules['password'] = 'required|min_length[6]';
                $rules['confirm_password'] = 'required|matches[password]';
            }

            if ($this->validate($rules)) {
                $updateData = [
                    'name' => $this->request->getPost('name'),
                    'email' => $this->request->getPost('email'),
                    'updated_at' => date('Y-m-d H:i:s')
                ];

                // Handle profile image upload
                $profileImage = $this->request->getFile('profile_image');
                if ($profileImage && $profileImage->isValid() && !$profileImage->hasMoved()) {
                    // Create directory if it doesn't exist
                    $uploadPath = FCPATH . 'uploads/profiles';
                    if (!is_dir($uploadPath)) {
                        mkdir($uploadPath, 0777, true);
                    }

                    // Generate a random name for the file
                    $newName = $profileImage->getRandomName();

                    // Move the file to the upload directory
                    $profileImage->move($uploadPath, $newName);

                    // Update the profile image path in the database
                    $updateData['profile_image'] = 'uploads/profiles/' . $newName;

                    // Delete old image if it exists and is not a URL (Google profile image)
                    if (!empty($user['profile_image']) && !filter_var($user['profile_image'], FILTER_VALIDATE_URL)) {
                        $oldImagePath = FCPATH . $user['profile_image'];
                        if (file_exists($oldImagePath)) {
                            unlink($oldImagePath);
                        }
                    }
                }

                // Update password if provided
                if ($this->request->getPost('password')) {
                    $updateData['password'] = password_hash($this->request->getPost('password'), PASSWORD_DEFAULT);
                }

                $this->userModel->update($userId, $updateData);

                // Update session data
                session()->set('name', $updateData['name']);
                session()->set('email', $updateData['email']);

                // Get the updated user data to ensure we have the latest profile image
                $updatedUser = $this->userModel->find($userId);
                if ($updatedUser && isset($updatedUser['profile_image'])) {
                    session()->set('profile_image', $updatedUser['profile_image']);
                }

                return redirect()->to('/profile')->with('success', 'Profile updated successfully.');
            } else {
                return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
            }
        }

        $data = [
            'title' => 'Edit Profile',
            'user' => $user
        ];

        return view('profile/edit', $data);
    }

    public function orders()
    {
        // Check if user is logged in
        if (!session()->get('isLoggedIn')) {
            return redirect()->to('/auth/login')->with('error', 'Please login to view your orders.');
        }

        $userId = session()->get('id');

        $orders = $this->orderModel->where('user_id', $userId)
            ->orderBy('created_at', 'DESC')
            ->findAll();

        $data = [
            'title' => 'My Orders',
            'orders' => $orders
        ];

        return view('profile/orders', $data);
    }

    public function viewOrder($id = null)
    {
        // Check if user is logged in
        if (!session()->get('isLoggedIn')) {
            return redirect()->to('/auth/login')->with('error', 'Please login to view your order.');
        }

        if (!$id) {
            return redirect()->to('/profile/orders')->with('error', 'Invalid order ID.');
        }

        $userId = session()->get('id');

        $order = $this->orderModel->where('id', $id)
            ->where('user_id', $userId)
            ->first();

        if (!$order) {
            return redirect()->to('/profile/orders')->with('error', 'Order not found.');
        }

        $orderItems = $this->orderItemModel->getOrderItems($id);

        $data = [
            'title' => 'Order #' . $id,
            'order' => $order,
            'orderItems' => $orderItems
        ];

        return view('profile/view_order', $data);
    }

    public function downloads()
    {
        // Check if user is logged in
        if (!session()->get('isLoggedIn')) {
            return redirect()->to('/auth/login')->with('error', 'Please login to view your downloads.');
        }

        $userId = session()->get('id');

        // Get all completed orders
        $completedOrders = $this->orderModel->where('user_id', $userId)
            ->where('payment_status', 'completed')
            ->findAll();

        $orderIds = array_column($completedOrders, 'id');

        // Get all papers from completed orders
        $downloads = [];

        if (!empty($orderIds)) {
            $db = \Config\Database::connect();

            $query = $db->query("
                SELECT
                    p.id,
                    p.title,
                    p.file_path,
                    p.preview_path,
                    c.name as category_name,
                    oi.order_id,
                    o.created_at as purchase_date
                FROM
                    papers p
                JOIN
                    order_items oi ON p.id = oi.paper_id
                JOIN
                    orders o ON oi.order_id = o.id
                JOIN
                    categories c ON p.category_id = c.id
                WHERE
                    o.user_id = {$userId}
                    AND o.payment_status = 'completed'
                    AND o.id IN (" . implode(',', $orderIds) . ")
                ORDER BY
                    o.created_at DESC
            ");

            $downloads = $query->getResultArray();
        }

        $data = [
            'title' => 'My Downloads',
            'downloads' => $downloads
        ];

        return view('profile/downloads', $data);
    }

    public function download($paperId = null)
    {
        // Check if user is logged in
        if (!session()->get('isLoggedIn')) {
            return redirect()->to('/auth/login')->with('error', 'Please login to download papers.');
        }

        if (!$paperId) {
            return redirect()->to('/profile/downloads')->with('error', 'Invalid paper ID.');
        }

        $userId = session()->get('id');

        // Check if user has purchased this paper
        $db = \Config\Database::connect();

        // Debug information
        log_message('info', 'Checking if user has purchased paper ID: ' . $paperId);
        log_message('info', 'User ID: ' . $userId);

        $query = $db->query("
            SELECT
                p.id,
                p.title,
                p.file_path
            FROM
                papers p
            JOIN
                order_items oi ON p.id = oi.paper_id
            JOIN
                orders o ON oi.order_id = o.id
            WHERE
                o.user_id = {$userId}
                AND o.payment_status = 'completed'
                AND p.id = {$paperId}
            LIMIT 1
        ");

        // Debug information
        log_message('info', 'Query result count: ' . count($query->getResultArray()));

        $paper = $query->getRowArray();

        if (!$paper) {
            return redirect()->to('/profile/downloads')->with('error', 'You have not purchased this paper or the payment is not completed.');
        }

        // Download the file
        // The file_path in the database only contains the filename, not the full path
        // We need to prepend the uploads/papers directory
        $filePath = FCPATH . 'uploads/papers/' . $paper['file_path'];

        // Debug information
        log_message('info', 'Attempting to download file: ' . $filePath);
        log_message('info', 'File exists check: ' . (file_exists($filePath) ? 'Yes' : 'No'));

        if (!file_exists($filePath)) {
            // Log the error for debugging
            log_message('error', 'File not found: ' . $filePath);
            return redirect()->to('/profile/downloads')->with('error', 'File not found. Please contact support.');
        }

        // Log successful download attempt
        log_message('info', 'Downloading file: ' . $filePath);

        // Return the file for download
        return $this->response->download($filePath, null);
    }
}
