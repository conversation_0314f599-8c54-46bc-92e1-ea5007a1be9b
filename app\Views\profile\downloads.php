<?= $this->extend('templates/main_layout') ?>

<?= $this->section('content') ?>
<div class="container py-5">
    <div class="row">
        <div class="col-lg-3">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">My Account</h5>
                </div>
                <div class="list-group list-group-flush">
                    <a href="<?= base_url('profile') ?>" class="list-group-item list-group-item-action">
                        <i class="fas fa-user me-2"></i>Profile
                    </a>
                    <a href="<?= base_url('profile/orders') ?>" class="list-group-item list-group-item-action">
                        <i class="fas fa-shopping-bag me-2"></i>Orders
                    </a>
                    <a href="<?= base_url('profile/downloads') ?>" class="list-group-item list-group-item-action active">
                        <i class="fas fa-download me-2"></i>Downloads
                    </a>
                    <a href="<?= base_url('auth/logout') ?>" class="list-group-item list-group-item-action text-danger">
                        <i class="fas fa-sign-out-alt me-2"></i>Logout
                    </a>
                </div>
            </div>
        </div>

        <div class="col-lg-9">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">My Downloads</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($downloads)): ?>
                        <div class="alert alert-info">
                            <p>You don't have any downloads available. <a href="<?= base_url('papers') ?>">Browse papers</a> to make a purchase.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Paper</th>
                                        <th>Preview</th>
                                        <th>Category</th>
                                        <th>Purchase Date</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($downloads as $download): ?>
                                        <tr>
                                            <td><?= esc($download['title']) ?></td>
                                            <td>
                                                <?php if ($download['preview_path']): ?>
                                                    <img src="<?= base_url("uploads/papers/previews/" . $download['preview_path']) ?>" alt="<?= esc($download['title']) ?>" style="max-width: 50px;">
                                                <?php else: ?>
                                                    <span class="text-muted">No preview</span>
                                                <?php endif; ?>
                                            </td>
                                            <td><?= esc($download['category_name']) ?></td>
                                            <td><?= date('M d, Y', strtotime($download['purchase_date'])) ?></td>
                                            <td>
                                                <a href="<?= base_url('profile/download/' . $download['id']) ?>" class="btn btn-success btn-sm" target="_blank">
                                                    <i class="fas fa-download"></i> Download
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>