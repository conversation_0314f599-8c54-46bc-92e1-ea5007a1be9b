<?= $this->extend('templates/main_layout') ?>

<?= $this->section('content') ?>

<!-- Hero Section -->
<section class="bg-light py-4 border-bottom">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6" data-aos="fade-right">
                <h1 class="h3 fw-bold mb-3">Browse Our Sample Papers</h1>
                <p class="text-muted mb-4">Find high-quality academic resources to enhance your research and studies</p>

                <!-- Search form -->
                <form action="<?= base_url('papers') ?>" method="get" id="searchForm" class="mb-0">
                    <div class="input-group">
                        <input type="text" class="form-control" placeholder="Search papers..." name="search" id="searchInput" value="<?= $search ?>">
                        <button class="btn btn-primary" type="submit"><i class="fas fa-search me-2"></i>Search</button>
                    </div>
                </form>
            </div>
            <div class="col-lg-6 d-none d-lg-block text-end" data-aos="fade-left">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb justify-content-end mb-0">
                        <li class="breadcrumb-item"><a href="<?= base_url() ?>" class="text-decoration-none">Home</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Papers</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>
</section>

<!-- Papers Listing -->
<section class="py-5">
    <div class="container">
        <div class="row g-4">
            <!-- Sidebar with filters -->
            <div class="col-lg-3">
                <div class="card border-0 shadow-sm mb-4 sticky-lg-top" style="top: 20px;">
                    <div class="card-header bg-white py-3">
                        <h5 class="mb-0 fw-bold fs-6">
                            <i class="fas fa-filter text-primary me-2"></i>Filter Papers
                        </h5>
                    </div>
                    <div class="card-body p-3">
                        <form action="<?= base_url('papers') ?>" method="get" id="filterForm">
                            <!-- Keep search term if present -->
                            <?php if (!empty($search)): ?>
                                <input type="hidden" name="search" value="<?= $search ?>">
                            <?php endif; ?>

                            <!-- Categories -->
                            <div class="mb-3">
                                <h6 class="fw-bold mb-2 small text-uppercase text-muted">Category</h6>
                                <div class="border-top pt-2">
                                    <select class="form-select form-select-sm" id="category" name="category">
                                        <option value="">All Categories</option>
                                        <?php foreach ($categories as $category): ?>
                                            <option value="<?= $category['id'] ?>" <?= ($selectedCategory == $category['id']) ? 'selected' : '' ?>>
                                                <?= $category['name'] ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>

                            <!-- Price Range -->
                            <div class="mb-3">
                                <h6 class="fw-bold mb-2 small text-uppercase text-muted">Price Range</h6>
                                <div class="border-top pt-2">
                                    <div class="row g-2">
                                        <div class="col-6">
                                            <div class="input-group input-group-sm">
                                                <span class="input-group-text">$</span>
                                                <input type="number" class="form-control" placeholder="Min" name="min_price" value="<?= $minPrice ?>" min="0">
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <div class="input-group input-group-sm">
                                                <span class="input-group-text">$</span>
                                                <input type="number" class="form-control" placeholder="Max" name="max_price" value="<?= $maxPrice ?>" min="0">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Sort By -->
                            <div class="mb-3">
                                <h6 class="fw-bold mb-2 small text-uppercase text-muted">Sort By</h6>
                                <div class="border-top pt-2">
                                    <select class="form-select form-select-sm" name="sort" id="sortSelect">
                                        <option value="newest">Newest First</option>
                                        <option value="price_low">Price: Low to High</option>
                                        <option value="price_high">Price: High to Low</option>
                                        <option value="title_asc">Title: A to Z</option>
                                    </select>
                                </div>
                            </div>

                            <div class="d-grid gap-2 mt-4">
                                <button type="submit" class="btn btn-primary btn-sm">
                                    <i class="fas fa-check me-1"></i> Apply Filters
                                </button>
                                <a href="<?= base_url('papers') ?>" class="btn btn-outline-secondary btn-sm">
                                    <i class="fas fa-times me-1"></i> Clear Filters
                                </a>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Popular Categories -->
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-white py-3">
                        <h5 class="mb-0 fw-bold fs-6">
                            <i class="fas fa-th-large text-primary me-2"></i>Popular Categories
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        <ul class="list-group list-group-flush">
                            <?php foreach (array_slice($categories, 0, 5) as $category): ?>
                                <li class="list-group-item d-flex justify-content-between align-items-center px-3 py-2">
                                    <a href="<?= base_url('papers?category=' . $category['id']) ?>" class="text-decoration-none text-dark small">
                                        <?= $category['name'] ?>
                                    </a>
                                    <span class="badge bg-primary rounded-pill">
                                        <?= rand(5, 25) ?>
                                    </span>
                                </li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Main content with papers -->
            <div class="col-lg-9">
                <?php if (!empty($search)): ?>
                    <div class="mb-4">
                        <h2 class="mb-2">Search Results for "<?= $search ?>"</h2>
                        <p class="text-muted">Showing <?= count($papers) ?> results</p>
                    </div>
                <?php endif; ?>

                <?php if (empty($papers)): ?>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>No papers found. Try adjusting your search criteria.
                    </div>
                <?php else: ?>
                    <div class="row g-3">
                        <?php foreach ($papers as $paper): ?>
                            <div class="col-md-6 mb-4" data-aos="fade-up">
                                <div class="card border-0 shadow-sm h-100">
                                    <div class="row g-0 h-100">
                                        <!-- Paper Image (Left Side) -->
                                        <div class="col-4 position-relative">
                                            <?php if (!empty($paper['preview_path'])): ?>
                                                <img src="<?= base_url('uploads/papers/previews/' . $paper['preview_path']) ?>"
                                                    alt="<?= $paper['title'] ?>"
                                                    class="rounded-start"
                                                    style="width: 100px; height: 100px; object-fit: cover;">
                                            <?php else: ?>
                                                <img src="https://images.unsplash.com/photo-1532012197267-da84d127e765?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&h=100&q=80"
                                                    alt="<?= $paper['title'] ?>"
                                                    class="rounded-start"
                                                    style="width: 100px; height: 100px; object-fit: cover;"
                                                    onerror="this.src='<?= base_url('assets/images/default-paper.jpg') ?>'; this.onerror=null;">
                                            <?php endif; ?>

                                            <!-- Category Badge -->
                                            <div class="position-absolute top-0 start-0">
                                                <span class="badge bg-primary bg-opacity-75 m-1 small"><?= $paper['category_name'] ?></span>
                                            </div>
                                        </div>

                                        <!-- Content (Right Side) -->
                                        <div class="col-8 d-flex flex-column">
                                            <div class="card-body py-2 px-3 d-flex flex-column h-100">
                                                <div class="d-flex justify-content-between align-items-start mb-1">
                                                    <h5 class="card-title h6 fw-bold mb-0 text-truncate"><?= $paper['title'] ?></h5>
                                                    <span class="badge bg-success rounded-pill ms-1">$<?= number_format($paper['price'], 2) ?></span>
                                                </div>

                                                <p class="card-text small text-muted mb-2 flex-grow-1" style="line-height: 1.4;">
                                                    <?= substr($paper['description'], 0, 80) . (strlen($paper['description']) > 80 ? '...' : '') ?>
                                                </p>

                                                <div class="d-flex justify-content-between align-items-center mt-auto">
                                                    <div class="text-muted small">
                                                        <i class="fas fa-file-alt text-primary me-1"></i> <?= rand(5, 20) ?> pages
                                                    </div>
                                                    <a href="<?= base_url('papers/view/' . $paper['id']) ?>" class="btn btn-sm btn-outline-primary py-1 px-2">
                                                        View <i class="fas fa-arrow-right ms-1 small"></i>
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>

<style>
    /* Category List Styling */
    .category-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .category-item {
        margin-bottom: 10px;
    }

    .category-link {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 15px;
        border-radius: 8px;
        color: var(--heading-color);
        text-decoration: none;
        transition: all 0.3s ease;
    }

    .category-link:hover,
    .category-link.active {
        background-color: rgba(67, 97, 238, 0.1);
        color: var(--accent-color);
        transform: translateX(5px);
    }

    .category-count {
        background-color: var(--accent-color);
        color: white;
        border-radius: 50px;
        padding: 2px 8px;
        font-size: 0.8rem;
        font-weight: 500;
    }

    /* Filter Sidebar */
    .filter-sidebar {
        background-color: var(--surface-color);
        border-radius: 15px;
        padding: 25px;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    }

    .filter-title {
        font-weight: 700;
        margin-bottom: 20px;
        padding-bottom: 15px;
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        color: var(--heading-color);
    }

    .filter-section {
        margin-bottom: 30px;
    }

    .filter-section:last-child {
        margin-bottom: 0;
    }
</style>

<?= $this->endSection() ?>