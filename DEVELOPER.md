# Developer Documentation - Sample Paper Store

This document provides detailed information for developers working on the Sample Paper Store application. It covers the technical aspects of the codebase, architecture, and guidelines for making modifications.

## Table of Contents

- [Architecture Overview](#architecture-overview)
- [Directory Structure](#directory-structure)
- [Core Components](#core-components)
- [Authentication System](#authentication-system)
- [Database Schema](#database-schema)
- [Frontend Structure](#frontend-structure)
- [Adding New Features](#adding-new-features)
- [Coding Standards](#coding-standards)
- [Testing](#testing)
- [Deployment](#deployment)

## Architecture Overview

Sample Paper Store is built using the CodeIgniter 4 PHP framework, which follows the Model-View-Controller (MVC) architectural pattern. The application uses:

- **PHP 7.4+** for server-side logic
- **MySQL** for database storage
- **Bootstrap 5** for frontend styling
- **jQuery** for client-side interactions
- **Google OAuth API** for social login

The application is structured to separate concerns:

- **Models** handle data access and business logic
- **Views** handle presentation
- **Controllers** handle request processing and response generation
- **Libraries** provide reusable functionality
- **Helpers** provide utility functions
- **Filters** handle request/response filtering

## Directory Structure

Detailed breakdown of the project structure:

```
sample-paper/
├── app/                    # Application code
│   ├── Config/             # Configuration files
│   │   ├── App.php         # Application configuration
│   │   ├── Auth.php        # Authentication configuration
│   │   ├── Database.php    # Database configuration
│   │   ├── Email.php       # Email configuration
│   │   ├── Filters.php     # HTTP filters configuration
│   │   ├── Google.php      # Google OAuth configuration
│   │   └── Routes.php      # Route definitions
│   ├── Controllers/        # Controller classes
│   │   ├── Admin/          # Admin controllers
│   │   ├── Auth.php        # Authentication controller
│   │   ├── BaseController.php # Base controller
│   │   ├── Categories.php  # Categories controller
│   │   ├── Home.php        # Home controller
│   │   ├── Papers.php      # Papers controller
│   │   └── Profile.php     # User profile controller
│   ├── Database/           # Database migrations and seeds
│   │   ├── Migrations/     # Database migrations
│   │   └── Seeds/          # Database seeds
│   ├── Filters/            # HTTP filters
│   │   ├── AdminFilter.php # Admin authentication filter
│   │   └── AuthFilter.php  # User authentication filter
│   ├── Helpers/            # Helper functions
│   ├── Libraries/          # Custom libraries
│   │   ├── CaptchaService.php # CAPTCHA generation service
│   │   ├── EmailService.php   # Email sending service
│   │   └── GoogleClient.php   # Google OAuth client
│   ├── Models/             # Model classes
│   │   ├── CategoryModel.php     # Category model
│   │   ├── OrderItemModel.php    # Order item model
│   │   ├── OrderModel.php        # Order model
│   │   ├── PaperModel.php        # Paper model
│   │   ├── PasswordResetAttemptModel.php # Password reset attempt model
│   │   ├── PasswordResetModel.php       # Password reset model
│   │   └── UserModel.php         # User model
│   └── Views/              # View templates
│       ├── admin/          # Admin views
│       ├── auth/           # Authentication views
│       ├── categories/     # Category views
│       ├── papers/         # Paper views
│       ├── profile/        # User profile views
│       └── templates/      # Layout templates
├── public/                 # Publicly accessible files
│   ├── assets/             # CSS, JS, images
│   │   ├── css/            # CSS files
│   │   ├── js/             # JavaScript files
│   │   └── images/         # Image files
│   ├── papers/             # Uploaded paper files
│   └── index.php           # Entry point
├── writable/               # Writable directory for logs, cache, etc.
│   ├── cache/              # Cache files
│   ├── logs/               # Log files
│   ├── session/            # Session files
│   └── uploads/            # Uploaded files
├── vendor/                 # Composer dependencies
├── composer.json           # Composer configuration
├── README.md               # General readme
└── DEVELOPER.md            # This file
```

## Core Components

### Controllers

Controllers handle HTTP requests and return responses. Key controllers include:

#### Auth Controller (`app/Controllers/Auth.php`)

Handles user authentication:
- Registration
- Login
- Google OAuth
- Password reset
- Profile management

```php
// Example: Login method
public function login()
{
    // Check if already logged in
    if (session()->get('isLoggedIn')) {
        return redirect()->to('/');
    }

    // Process form submission
    if ($this->request->getMethod() === 'post') {
        // Validation and authentication logic
    }

    // Display login form
    return view('auth/login', $data);
}
```

#### Papers Controller (`app/Controllers/Papers.php`)

Handles paper-related functionality:
- Listing papers
- Viewing paper details
- Searching and filtering
- Downloading papers

#### Categories Controller (`app/Controllers/Categories.php`)

Handles category-related functionality:
- Listing categories
- Viewing papers by category

#### Admin Controllers (`app/Controllers/Admin/`)

Handle admin functionality:
- Dashboard
- Paper management
- Category management
- User management
- Order management

### Models

Models handle data access and business logic. Key models include:

#### UserModel (`app/Models/UserModel.php`)

Handles user data:
- User registration
- User authentication
- User profile management

```php
// Example: Get user by email
public function getUserByEmail($email)
{
    return $this->where('email', $email)->first();
}
```

#### PaperModel (`app/Models/PaperModel.php`)

Handles paper data:
- Paper listing
- Paper searching
- Paper filtering

#### CategoryModel (`app/Models/CategoryModel.php`)

Handles category data:
- Category listing
- Getting papers by category

### Libraries

Libraries provide reusable functionality:

#### GoogleClient (`app/Libraries/GoogleClient.php`)

Handles Google OAuth integration:
- Generating login URLs
- Processing OAuth callbacks
- Retrieving user information

```php
// Example: Get user login URL
public function getUserLoginUrl()
{
    $redirectUri = base_url($this->config->redirectUri);
    $this->client->setRedirectUri($redirectUri);
    return $this->client->createAuthUrl();
}
```

#### EmailService (`app/Libraries/EmailService.php`)

Handles email sending:
- Password reset emails
- Order confirmation emails
- Contact form emails

#### CaptchaService (`app/Libraries/CaptchaService.php`)

Handles CAPTCHA generation and verification:
- Generating math CAPTCHAs
- Verifying CAPTCHA responses

### Views

Views handle presentation. Key view directories include:

#### Auth Views (`app/Views/auth/`)

Authentication-related views:
- Login form
- Registration form
- Forgot password form
- Reset password form

#### Paper Views (`app/Views/papers/`)

Paper-related views:
- Paper listing
- Paper details
- Paper search results

#### Category Views (`app/Views/categories/`)

Category-related views:
- Category listing
- Papers by category

#### Admin Views (`app/Views/admin/`)

Admin-related views:
- Dashboard
- Paper management
- Category management
- User management

#### Template Views (`app/Views/templates/`)

Layout templates:
- Main layout
- Admin layout
- Email templates

## Authentication System

The authentication system is implemented in `app/Controllers/Auth.php` and includes:

### User Registration

```php
public function register()
{
    // Registration logic
}
```

### User Login

```php
public function login()
{
    // Login logic
}
```

### Google OAuth Integration

```php
public function googleCallback()
{
    // Google OAuth callback logic
}
```

### Password Reset

The password reset functionality is implemented using:

1. **PasswordResetModel**: Stores reset tokens
2. **EmailService**: Sends reset emails
3. **Rate limiting**: Prevents abuse

```php
public function forgotPassword()
{
    // Forgot password form
}

public function processForgotPassword()
{
    // Process forgot password request
}

public function resetPassword($token)
{
    // Reset password form
}

public function processResetPassword()
{
    // Process reset password request
}
```

### Authentication Filters

Authentication is enforced using filters:

- **AuthFilter**: Ensures users are logged in
- **AdminFilter**: Ensures users are admins

## Database Schema

### Users Table

```sql
CREATE TABLE users (
    id INT(11) UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    is_admin ENUM('admin', 'user') DEFAULT 'user',
    status ENUM('active', 'inactive') DEFAULT 'active',
    google_id VARCHAR(100) NULL,
    oauth_provider VARCHAR(50) NULL,
    profile_image VARCHAR(255) NULL,
    created_at DATETIME NULL,
    updated_at DATETIME NULL
);
```

### Categories Table

```sql
CREATE TABLE categories (
    id INT(11) UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    slug VARCHAR(100) NOT NULL UNIQUE,
    description TEXT NULL,
    preview_image VARCHAR(255) NULL,
    created_at DATETIME NULL,
    updated_at DATETIME NULL
);
```

### Papers Table

```sql
CREATE TABLE papers (
    id INT(11) UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    slug VARCHAR(255) NOT NULL UNIQUE,
    description TEXT NULL,
    author VARCHAR(100) NULL,
    category_id INT(11) UNSIGNED NOT NULL,
    price DECIMAL(10, 2) NOT NULL,
    file_path VARCHAR(255) NOT NULL,
    preview_path VARCHAR(255) NULL,
    preview_image VARCHAR(255) NULL,
    pages INT(11) NULL,
    word_count INT(11) NULL,
    format VARCHAR(50) NULL,
    published_date DATE NULL,
    featured TINYINT(1) DEFAULT 0,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at DATETIME NULL,
    updated_at DATETIME NULL,
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE CASCADE
);
```

### Password Reset Tables

```sql
CREATE TABLE password_resets (
    id INT(11) UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    email VARCHAR(100) NOT NULL,
    token VARCHAR(255) NOT NULL,
    created_at DATETIME NULL,
    expires_at DATETIME NULL
);

CREATE TABLE password_reset_attempts (
    id INT(11) UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    ip_address VARCHAR(45) NOT NULL,
    email VARCHAR(100) NOT NULL,
    created_at DATETIME NULL
);
```

## Frontend Structure

### Templates

The application uses two main templates:

1. **Main Layout** (`app/Views/templates/main_layout.php`): Used for user-facing pages
2. **Admin Layout** (`app/Views/templates/admin_layout.php`): Used for admin pages

### CSS and JavaScript

CSS and JavaScript files are stored in `public/assets/`:

- **CSS**: `public/assets/css/`
  - Bootstrap 5
  - Custom styles

- **JavaScript**: `public/assets/js/`
  - jQuery
  - Bootstrap JS
  - Custom scripts

### Images

Images are stored in `public/assets/images/`:

- Logo
- Default images
- UI elements

## Adding New Features

### Adding a New Page

1. Create a new method in the appropriate controller:

```php
public function newFeature()
{
    $data = [
        'title' => 'New Feature - Sample Paper Store'
    ];
    
    return view('new_feature', $data);
}
```

2. Create a new view file:

```php
<?= $this->extend('templates/main_layout') ?>

<?= $this->section('content') ?>
<div class="container py-5">
    <h1>New Feature</h1>
    <!-- Content here -->
</div>
<?= $this->endSection() ?>
```

3. Add a route in `app/Config/Routes.php`:

```php
$routes->get('new-feature', 'Controller::newFeature');
```

### Adding a New Model

1. Create a new model file:

```php
namespace App\Models;

use CodeIgniter\Model;

class NewModel extends Model
{
    protected $table = 'new_table';
    protected $primaryKey = 'id';
    protected $allowedFields = ['field1', 'field2', 'field3'];
    protected $useTimestamps = true;
    protected $returnType = 'array';
    
    // Custom methods
    public function getItems()
    {
        return $this->findAll();
    }
}
```

2. Create a migration for the new table:

```php
namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateNewTable extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'field1' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
            ],
            'field2' => [
                'type' => 'TEXT',
                'null' => true,
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'updated_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
        ]);
        $this->forge->addKey('id', true);
        $this->forge->createTable('new_table');
    }

    public function down()
    {
        $this->forge->dropTable('new_table');
    }
}
```

### Adding a New Controller

1. Create a new controller file:

```php
namespace App\Controllers;

use App\Models\NewModel;

class NewController extends BaseController
{
    protected $newModel;
    
    public function __construct()
    {
        $this->newModel = new NewModel();
    }
    
    public function index()
    {
        $data = [
            'title' => 'New Feature - Sample Paper Store',
            'items' => $this->newModel->getItems()
        ];
        
        return view('new/index', $data);
    }
    
    public function view($id = null)
    {
        $item = $this->newModel->find($id);
        
        if ($item === null) {
            throw \CodeIgniter\Exceptions\PageNotFoundException::forPageNotFound();
        }
        
        $data = [
            'title' => $item['field1'] . ' - Sample Paper Store',
            'item' => $item
        ];
        
        return view('new/view', $data);
    }
}
```

2. Add routes for the new controller:

```php
$routes->get('new', 'NewController::index');
$routes->get('new/view/(:num)', 'NewController::view/$1');
```

## Coding Standards

The project follows the PSR-12 coding standard. Key points:

- Use 4 spaces for indentation
- Use camelCase for method names
- Use PascalCase for class names
- Use snake_case for database fields
- Add appropriate comments and docblocks

## Testing

### Manual Testing

For manual testing:

1. Start the development server:
   ```bash
   php spark serve
   ```

2. Access the application at `http://localhost:8080`

3. Test the following features:
   - User registration
   - User login
   - Google login
   - Password reset
   - Paper browsing
   - Paper searching
   - Paper downloading
   - Admin functionality

### Automated Testing

For automated testing, create test files in the `tests/` directory:

```php
namespace Tests\Feature;

use CodeIgniter\Test\FeatureTestCase;

class AuthTest extends FeatureTestCase
{
    public function testLogin()
    {
        $result = $this->post('auth/login', [
            'email' => '<EMAIL>',
            'password' => 'password123'
        ]);
        
        $result->assertRedirect('/');
        $result->assertSessionHas('isLoggedIn', true);
    }
}
```

Run tests using:

```bash
php spark test
```

## Deployment

### Production Configuration

For production deployment:

1. Update the `.env` file:
   ```
   CI_ENVIRONMENT = production
   app.baseURL = 'https://your-production-url.com/'
   ```

2. Configure the database:
   ```
   database.default.hostname = production-db-host
   database.default.database = production-db-name
   database.default.username = production-db-user
   database.default.password = production-db-password
   ```

3. Configure email settings:
   ```
   email.protocol = smtp
   email.SMTPHost = production-smtp-host
   email.SMTPUser = production-smtp-user
   email.SMTPPass = production-smtp-password
   ```

4. Configure Google OAuth:
   ```
   google.clientId = production-client-id
   google.clientSecret = production-client-secret
   ```

### Deployment Steps

1. Clone the repository on the production server
2. Install dependencies:
   ```bash
   composer install --no-dev
   ```
3. Set up the production configuration
4. Run migrations:
   ```bash
   php spark migrate
   ```
5. Set appropriate permissions:
   ```bash
   chmod -R 755 writable/
   ```
6. Configure the web server (Apache or Nginx) to point to the `public/` directory

### Apache Configuration

```apache
<VirtualHost *:80>
    ServerName your-production-url.com
    DocumentRoot /path/to/sample-paper/public
    
    <Directory /path/to/sample-paper/public>
        AllowOverride All
        Require all granted
    </Directory>
</VirtualHost>
```

### Nginx Configuration

```nginx
server {
    listen 80;
    server_name your-production-url.com;
    root /path/to/sample-paper/public;
    
    index index.php index.html index.htm;
    
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }
    
    location ~ \.php$ {
        include snippets/fastcgi-php.conf;
        fastcgi_pass unix:/var/run/php/php7.4-fpm.sock;
    }
}
```

## Conclusion

This documentation provides a comprehensive guide to the Sample Paper Store application. For any questions or issues, please contact the development team.
