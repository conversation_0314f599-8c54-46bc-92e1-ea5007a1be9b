<?php

namespace App\Controllers\Api;

use App\Controllers\BaseController;
use App\Models\PaperModel;
use App\Models\CategoryModel;
use CodeIgniter\API\ResponseTrait;

class PaperController extends BaseController
{
    use ResponseTrait;

    protected $paperModel;
    protected $categoryModel;

    public function __construct()
    {
        $this->paperModel = new PaperModel();
        $this->categoryModel = new CategoryModel();
    }

    /**
     * Get all papers
     *
     * @return \CodeIgniter\HTTP\Response
     */
    public function index()
    {
        try {
            $papers = $this->paperModel->select('papers.*, categories.name as category_name')
                ->join('categories', 'categories.id = papers.category_id')
                ->where('papers.status', 'active')
                ->orderBy('papers.created_at', 'DESC')
                ->findAll();

            // Format papers data
            $formattedPapers = [];
            foreach ($papers as $paper) {
                $formattedPapers[] = [
                    'id' => $paper['id'],
                    'title' => $paper['title'],
                    'description' => $paper['description'],
                    'category_id' => $paper['category_id'],
                    'category_name' => $paper['category_name'],
                    'price' => $paper['price'],
                    'preview_path' => $paper['preview_path'] ? (strpos($paper['preview_path'], 'uploads/papers/previews/') === 0 ? base_url($paper['preview_path']) : base_url('uploads/papers/previews/' . $paper['preview_path'])) : null,
                    'created_at' => $paper['created_at'],
                    'updated_at' => $paper['updated_at']
                ];
            }

            return $this->respond([
                'status' => true,
                'papers' => $formattedPapers
            ]);
        } catch (\Exception $e) {
            log_message('error', 'Get papers error: ' . $e->getMessage());
            return $this->fail('Failed to retrieve papers: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Get paper details
     *
     * @param int $id
     * @return \CodeIgniter\HTTP\Response
     */
    public function show($id)
    {
        try {
            $paper = $this->paperModel->select('papers.*, categories.name as category_name')
                ->join('categories', 'categories.id = papers.category_id')
                ->where('papers.id', $id)
                ->where('papers.status', 'active')
                ->first();

            if (!$paper) {
                return $this->failNotFound('Paper not found.');
            }

            // Format paper data
            $formattedPaper = [
                'id' => $paper['id'],
                'title' => $paper['title'],
                'description' => $paper['description'],
                'category_id' => $paper['category_id'],
                'category_name' => $paper['category_name'],
                'price' => $paper['price'],
                'preview_path' => $paper['preview_path'] ? (strpos($paper['preview_path'], 'uploads/papers/previews/') === 0 ? base_url($paper['preview_path']) : base_url('uploads/papers/previews/' . $paper['preview_path'])) : null,
                'created_at' => $paper['created_at'],
                'updated_at' => $paper['updated_at']
            ];

            return $this->respond([
                'status' => true,
                'paper' => $formattedPaper
            ]);
        } catch (\Exception $e) {
            log_message('error', 'Get paper details error: ' . $e->getMessage());
            return $this->fail('Failed to retrieve paper details: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Get papers by category
     *
     * @param int $categoryId
     * @return \CodeIgniter\HTTP\Response
     */
    public function getByCategory($categoryId)
    {
        try {
            // Check if category exists
            $category = $this->categoryModel->find($categoryId);

            if (!$category) {
                return $this->failNotFound('Category not found.');
            }

            $papers = $this->paperModel->select('papers.*, categories.name as category_name')
                ->join('categories', 'categories.id = papers.category_id')
                ->where('papers.category_id', $categoryId)
                ->where('papers.status', 'active')
                ->orderBy('papers.created_at', 'DESC')
                ->findAll();

            // Format papers data
            $formattedPapers = [];
            foreach ($papers as $paper) {
                $formattedPapers[] = [
                    'id' => $paper['id'],
                    'title' => $paper['title'],
                    'description' => $paper['description'],
                    'category_id' => $paper['category_id'],
                    'category_name' => $paper['category_name'],
                    'price' => $paper['price'],
                    'preview_path' => $paper['preview_path'] ? (strpos($paper['preview_path'], 'uploads/papers/previews/') === 0 ? base_url($paper['preview_path']) : base_url('uploads/papers/previews/' . $paper['preview_path'])) : null,
                    'created_at' => $paper['created_at'],
                    'updated_at' => $paper['updated_at']
                ];
            }

            return $this->respond([
                'status' => true,
                'category' => [
                    'id' => $category['id'],
                    'name' => $category['name'],
                    'description' => $category['description']
                ],
                'papers' => $formattedPapers
            ]);
        } catch (\Exception $e) {
            log_message('error', 'Get papers by category error: ' . $e->getMessage());
            return $this->fail('Failed to retrieve papers: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Search papers
     *
     * @return \CodeIgniter\HTTP\Response
     */
    public function search()
    {
        $keyword = $this->request->getGet('keyword');

        if (empty($keyword)) {
            return $this->fail('Search keyword is required.', 400);
        }

        try {
            $papers = $this->paperModel->select('papers.*, categories.name as category_name')
                ->join('categories', 'categories.id = papers.category_id')
                ->like('papers.title', $keyword)
                ->orLike('papers.description', $keyword)
                ->where('papers.status', 'active')
                ->orderBy('papers.created_at', 'DESC')
                ->findAll();

            // Format papers data
            $formattedPapers = [];
            foreach ($papers as $paper) {
                $formattedPapers[] = [
                    'id' => $paper['id'],
                    'title' => $paper['title'],
                    'description' => $paper['description'],
                    'category_id' => $paper['category_id'],
                    'category_name' => $paper['category_name'],
                    'price' => $paper['price'],
                    'preview_path' => $paper['preview_path'] ? (strpos($paper['preview_path'], 'uploads/papers/previews/') === 0 ? base_url($paper['preview_path']) : base_url('uploads/papers/previews/' . $paper['preview_path'])) : null,
                    'created_at' => $paper['created_at'],
                    'updated_at' => $paper['updated_at']
                ];
            }

            return $this->respond([
                'status' => true,
                'keyword' => $keyword,
                'papers' => $formattedPapers
            ]);
        } catch (\Exception $e) {
            log_message('error', 'Search papers error: ' . $e->getMessage());
            return $this->fail('Failed to search papers: ' . $e->getMessage(), 500);
        }
    }
}
