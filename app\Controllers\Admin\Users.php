<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Models\UserModel;

class Users extends BaseController
{
    protected $userModel;

    public function __construct()
    {
        $this->userModel = new UserModel();
    }

    public function index()
    {
        $data = [
            'title' => 'Users Management',
            'users' => $this->userModel->paginate(10),
            'pager' => $this->userModel->pager
        ];

        return view('admin/users/index', $data);
    }

    public function view($id = null)
    {
        if ($id === null) {
            return redirect()->to('/admin/users')->with('error', 'User ID is required.');
        }

        $user = $this->userModel->getUserById($id);

        if (!$user) {
            return redirect()->to('/admin/users')->with('error', 'User not found.');
        }

        $data = [
            'title' => 'View User',
            'user' => $user
        ];

        return view('admin/users/view', $data);
    }

    public function edit($id = null)
    {
        if ($id === null) {
            return redirect()->to('/admin/users')->with('error', 'User ID is required.');
        }

        $user = $this->userModel->getUserById($id);

        if (!$user) {
            return redirect()->to('/admin/users')->with('error', 'User not found.');
        }

        if ($this->request->getMethod() === 'post') {
            $rules = [
                'name' => 'required|min_length[3]|max_length[255]',
                'email' => 'required|valid_email',
                'role' => 'required|in_list[user,admin]',
                'phone' => 'permit_empty|min_length[10]|max_length[15]',
                'address' => 'permit_empty|max_length[255]'
            ];

            if ($this->validate($rules)) {
                $updateData = [
                    'name' => $this->request->getPost('name'),
                    'email' => $this->request->getPost('email'),
                    'role' => $this->request->getPost('role'),
                    'phone' => $this->request->getPost('phone'),
                    'address' => $this->request->getPost('address'),
                    'updated_at' => date('Y-m-d H:i:s')
                ];

                $this->userModel->update($id, $updateData);
                return redirect()->to('/admin/users')->with('success', 'User updated successfully.');
            }
        }

        $data = [
            'title' => 'Edit User',
            'user' => $user,
            'validation' => $this->validator
        ];

        return view('admin/users/edit', $data);
    }

    public function delete($id = null)
    {
        if ($id === null) {
            return redirect()->to('/admin/users')->with('error', 'User ID is required.');
        }

        $user = $this->userModel->getUserById($id);

        if (!$user) {
            return redirect()->to('/admin/users')->with('error', 'User not found.');
        }

        // Prevent deleting own account
        if ($user['id'] === session()->get('user_id')) {
            return redirect()->to('/admin/users')->with('error', 'You cannot delete your own account.');
        }

        $this->userModel->delete($id);
        return redirect()->to('/admin/users')->with('success', 'User deleted successfully.');
    }

    public function ban($id = null)
    {
        if ($id === null) {
            return redirect()->to('/admin/users')->with('error', 'User ID is required.');
        }

        $user = $this->userModel->getUserById($id);

        if (!$user) {
            return redirect()->to('/admin/users')->with('error', 'User not found.');
        }

        // Prevent banning own account
        if ($user['id'] === session()->get('user_id')) {
            return redirect()->to('/admin/users')->with('error', 'You cannot ban your own account.');
        }

        $this->userModel->update($id, ['status' => 'banned']);
        return redirect()->to('/admin/users')->with('success', 'User banned successfully.');
    }

    public function unban($id = null)
    {
        if ($id === null) {
            return redirect()->to('/admin/users')->with('error', 'User ID is required.');
        }

        $user = $this->userModel->getUserById($id);

        if (!$user) {
            return redirect()->to('/admin/users')->with('error', 'User not found.');
        }

        $this->userModel->update($id, ['status' => 'active']);
        return redirect()->to('/admin/users')->with('success', 'User unbanned successfully.');
    }
}