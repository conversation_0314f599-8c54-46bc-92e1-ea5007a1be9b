<?php

namespace App\Controllers;

use App\Models\PaperModel;
use App\Models\CategoryModel;

class Papers extends BaseController
{
    public function index()
    {
        $paperModel = new PaperModel();
        $categoryModel = new CategoryModel();

        $search = $this->request->getGet('search');
        $categoryId = $this->request->getGet('category');
        $minPrice = $this->request->getGet('min_price');
        $maxPrice = $this->request->getGet('max_price');
        $year = $this->request->getGet('year');

        $filters = [
            'category_id' => $categoryId,
            'min_price' => $minPrice,
            'max_price' => $maxPrice,
            'year' => $year
        ];

        $papers = $paperModel->searchPapers($search, $filters);
        $categories = $categoryModel->findAll();
        $availableYears = $paperModel->getAvailableYears();

        $data = [
            'title' => 'Browse Papers - Sample Paper Store',
            'papers' => $papers,
            'categories' => $categories,
            'availableYears' => $availableYears,
            'search' => $search,
            'selectedCategory' => $categoryId,
            'selectedYear' => $year,
            'minPrice' => $minPrice,
            'maxPrice' => $maxPrice,
            'paperModel' => $paperModel  // Add this line to pass the paperModel to the view
        ];

        return view('papers/index', $data);
    }

    public function view($id = null)
    {
        if ($id === null) {
            return redirect()->to('/papers');
        }

        $paperModel = new PaperModel();
        $paper = $paperModel->getPaperWithCategory($id);

        if ($paper === null) {
            return redirect()->to('/papers')->with('error', 'Paper not found');
        }

        // Get related papers from the same category
        $relatedPapers = $paperModel->where('category_id', $paper['category_id'])
                                   ->where('id !=', $id)
                                   ->where('status', 'active')
                                   ->orderBy('created_at', 'DESC')
                                   ->limit(3)
                                   ->findAll();

        $data = [
            'title' => $paper['title'] . ' - Sample Paper Store',
            'paper' => $paper,
            'relatedPapers' => $relatedPapers
        ];

        return view('papers/view', $data);
    }

    public function latest()
    {
        $paperModel = new PaperModel();
        $categoryModel = new CategoryModel();

        $papers = $paperModel->getLatestPapersWithCategories(20);
        $categories = $categoryModel->findAll();

        $data = [
            'title' => 'Latest Papers - Sample Paper Store',
            'papers' => $papers,
            'categories' => $categories
        ];

        return view('papers/latest', $data);
    }

    public function category($id = null)
    {
        if ($id === null) {
            return redirect()->to('/categories');
        }

        $categoryModel = new CategoryModel();
        $paperModel = new PaperModel();

        $category = $categoryModel->find($id);

        if ($category === null) {
            return redirect()->to('/categories')->with('error', 'Category not found');
        }

        $papers = $paperModel->getPapersByCategory($id);
        $categories = $categoryModel->findAll();

        $data = [
            'title' => $category['name'] . ' Papers - Sample Paper Store',
            'category' => $category,
            'papers' => $papers,
            'categories' => $categories
        ];

        return view('papers/category', $data);
    }

    public function download($id = null)
    {
        // Check if user is logged in
        if (!session()->get('isLoggedIn')) {
            return redirect()->to('auth/login')->with('error', 'Please login to download papers');
        }

        if ($id === null) {
            return redirect()->to('/papers');
        }

        $paperModel = new PaperModel();
        $paper = $paperModel->find($id);

        if ($paper === null) {
            return redirect()->to('/papers')->with('error', 'Paper not found');
        }

        // Check if user has purchased this paper
        // This would typically involve checking the orders table
        // For now, we'll just allow the download

        // Get the file path
        $filePath = FCPATH . 'uploads/papers/' . $paper['file_path'];

        if (!file_exists($filePath)) {
            return redirect()->to('/papers/view/' . $id)->with('error', 'File not found');
        }

        // Log the download
        log_message('info', 'User ID ' . session()->get('id') . ' downloaded paper ID ' . $id);

        // Return the file for download
        return $this->response->download($filePath, null);
    }

    public function preview($id = null)
    {
        if ($id === null) {
            return redirect()->to('/papers');
        }

        $paperModel = new PaperModel();
        $paper = $paperModel->find($id);

        if ($paper === null) {
            return redirect()->to('/papers')->with('error', 'Paper not found');
        }

        // Get the preview file path
        $previewPath = FCPATH . 'uploads/previews/' . $paper['preview_path'];

        if (!file_exists($previewPath)) {
            return redirect()->to('/papers/view/' . $id)->with('error', 'Preview not available');
        }

        // Return the preview file
        return $this->response->download($previewPath, null);
    }
}