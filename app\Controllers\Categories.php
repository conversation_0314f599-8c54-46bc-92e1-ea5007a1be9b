<?php

namespace App\Controllers;

use App\Models\CategoryModel;
use App\Models\PaperModel;

class Categories extends BaseController
{
    protected $categoryModel;
    protected $paperModel;

    public function __construct()
    {
        $this->categoryModel = new CategoryModel();
        $this->paperModel = new PaperModel();
    }

    public function index()
    {
        $categories = $this->categoryModel->findAll();

        $data = [
            'title' => 'Categories - Sample Paper Store',
            'categories' => $categories
        ];

        return view('categories/index', $data);
    }

    public function view($id = null)
    {
        if ($id === null) {
            return redirect()->to('/papers');
        }

        $category = $this->categoryModel->find($id);

        if ($category === null) {
            throw \CodeIgniter\Exceptions\PageNotFoundException::forPageNotFound();
        }

        $papers = $this->paperModel->getPapersByCategory($id);
        $categories = $this->categoryModel->findAll(); // For sidebar

        $data = [
            'category' => $category,
            'papers' => $papers,
            'categories' => $categories,
            'paperModel' => $this->paperModel,
            'title' => $category['name'] . ' - Papers'
        ];

        return view('categories/view', $data);
    }
}