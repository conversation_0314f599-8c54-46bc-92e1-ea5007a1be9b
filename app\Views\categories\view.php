<?= $this->extend('templates/main_layout') ?>

<?= $this->section('content') ?>

<!-- Category Header -->
<div class="bg-light py-4 border-bottom">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h1 class="h3 fw-bold mb-0"><?= $category['name'] ?></h1>
            </div>
            <div class="col-md-6">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb mb-0 justify-content-md-end">
                        <li class="breadcrumb-item"><a href="<?= base_url() ?>" class="text-decoration-none">Home</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('categories') ?>" class="text-decoration-none">Categories</a></li>
                        <li class="breadcrumb-item active" aria-current="page"><?= $category['name'] ?></li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>
</div>

<div class="container py-5">
    <div class="row g-4">
        <!-- Sidebar with categories -->
        <div class="col-lg-3">
            <!-- Category Sidebar -->
            <div class="card border-0 shadow-sm mb-4 sticky-lg-top" style="top: 2rem; z-index: 100;">
                <div class="card-header bg-white py-3">
                    <h5 class="mb-0 fw-bold">
                        <i class="fas fa-th-large text-primary me-2"></i>Categories
                    </h5>
                </div>
                <div class="card-body p-0">
                    <ul class="list-group list-group-flush">
                        <?php foreach ($categories as $cat): ?>
                            <li class="list-group-item d-flex justify-content-between align-items-center px-3 py-2 <?= ($cat['id'] == $category['id']) ? 'bg-primary bg-opacity-10' : '' ?>">
                                <a href="<?= base_url('categories/view/' . $cat['id']) ?>" class="text-decoration-none <?= ($cat['id'] == $category['id']) ? 'text-primary fw-medium' : 'text-dark' ?>">
                                    <?php
                                    // Assign an icon based on category name or id
                                    $icons = ['book', 'flask', 'calculator', 'language', 'chart-line', 'landmark', 'microscope', 'heartbeat', 'globe', 'laptop-code'];
                                    $iconIndex = $cat['id'] % count($icons);
                                    $icon = $icons[$iconIndex];
                                    ?>
                                    <i class="fas fa-<?= $icon ?> me-2 <?= ($cat['id'] == $category['id']) ? 'text-primary' : 'text-muted' ?>"></i>
                                    <?= $cat['name'] ?>
                                </a>
                                <span class="badge <?= ($cat['id'] == $category['id']) ? 'bg-primary' : 'bg-secondary bg-opacity-10 text-secondary' ?> rounded-pill">
                                    <?= count($paperModel->getPapersByCategory($cat['id'])) ?>
                                </span>
                            </li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            </div>

            <!-- Search in Category -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white py-3">
                    <h5 class="mb-0 fw-bold">
                        <i class="fas fa-search text-primary me-2"></i>Search in Category
                    </h5>
                </div>
                <div class="card-body p-3">
                    <form action="<?= base_url('categories/view/' . $category['id']) ?>" method="get">
                        <div class="input-group">
                            <input type="text" class="form-control" placeholder="Search papers..." name="search" value="<?= isset($_GET['search']) ? $_GET['search'] : '' ?>">
                            <button class="btn btn-primary" type="submit">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Main content with papers -->
        <div class="col-lg-9">
            <!-- Category Description -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-3">
                        <?php
                        // Use the same icon logic as above
                        $icons = ['book', 'flask', 'calculator', 'language', 'chart-line', 'landmark', 'microscope', 'heartbeat', 'globe', 'laptop-code'];
                        $iconIndex = $category['id'] % count($icons);
                        $icon = $icons[$iconIndex];
                        ?>
                        <div class="d-flex align-items-center justify-content-center bg-primary bg-opacity-10 rounded-circle me-3" style="width: 48px; height: 48px;">
                            <i class="fas fa-<?= $icon ?> text-primary fa-lg"></i>
                        </div>
                        <div>
                            <h2 class="h4 fw-bold mb-1"><?= $category['name'] ?></h2>
                            <p class="text-muted mb-0">
                                <i class="fas fa-file-alt me-1"></i>
                                <?= count($papers) ?> papers available
                            </p>
                        </div>
                    </div>
                    <p class="mb-0"><?= $category['description'] ?? 'Browse our collection of high-quality academic papers in this category.' ?></p>
                </div>
            </div>

            <!-- Sorting Options -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h3 class="h5 fw-bold mb-0">
                        <?php if (empty($papers)): ?>
                            No Papers Available
                        <?php else: ?>
                            Available Papers
                        <?php endif; ?>
                    </h3>
                </div>
                <div>
                    <select class="form-select form-select-sm" id="sortOptions" onchange="window.location.href=this.value">
                        <option value="<?= base_url('categories/view/' . $category['id'] . '?sort=newest') ?>" <?= (isset($_GET['sort']) && $_GET['sort'] == 'newest') ? 'selected' : '' ?>>Newest First</option>
                        <option value="<?= base_url('categories/view/' . $category['id'] . '?sort=price_low') ?>" <?= (isset($_GET['sort']) && $_GET['sort'] == 'price_low') ? 'selected' : '' ?>>Price: Low to High</option>
                        <option value="<?= base_url('categories/view/' . $category['id'] . '?sort=price_high') ?>" <?= (isset($_GET['sort']) && $_GET['sort'] == 'price_high') ? 'selected' : '' ?>>Price: High to Low</option>
                        <option value="<?= base_url('categories/view/' . $category['id'] . '?sort=title_asc') ?>" <?= (isset($_GET['sort']) && $_GET['sort'] == 'title_asc') ? 'selected' : '' ?>>Title: A to Z</option>
                    </select>
                </div>
            </div>

            <!-- Papers Listing -->
            <?php if (empty($papers)): ?>
                <div class="card border-0 shadow-sm py-5">
                    <div class="card-body text-center py-5">
                        <div class="mb-4">
                            <img src="https://cdn-icons-png.flaticon.com/512/7486/7486754.png" alt="No Papers"
                                class="img-fluid mb-4" style="max-height: 150px; opacity: 0.7;">
                        </div>
                        <h3 class="h4 fw-bold mb-3">No Papers Available</h3>
                        <p class="text-muted mb-4 px-md-5 mx-md-5">There are no papers available in this category at the moment. Please check back later or browse other categories.</p>
                        <a href="<?= base_url('categories') ?>" class="btn btn-primary px-4 py-2">
                            <i class="fas fa-th-large me-2"></i>Browse All Categories
                        </a>
                    </div>
                </div>
            <?php else: ?>
                <div class="row g-4">
                    <?php foreach ($papers as $paper): ?>
                        <div class="col-md-6 mb-4">
                            <div class="card h-100 border-0 shadow-sm">
                                <div class="row g-0 h-100">
                                    <!-- Paper Image -->
                                    <div class="col-4 position-relative">
                                        <?php if (!empty($paper['preview_path'])): ?>
                                            <img src="<?= base_url('uploads/papers/previews/' . $paper['preview_path']) ?>"
                                                alt="<?= $paper['title'] ?>"
                                                class="rounded-start"
                                                style="width: 100px; height: 100px; object-fit: cover;">
                                        <?php else: ?>
                                            <img src="https://images.unsplash.com/photo-1532012197267-da84d127e765?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&h=100&q=80"
                                                alt="<?= $paper['title'] ?>"
                                                class="rounded-start"
                                                style="width: 100px; height: 100px; object-fit: cover;">
                                        <?php endif; ?>
                                        <div class="position-absolute top-0 start-0">
                                            <span class="badge bg-primary bg-opacity-75 m-1 small"><?= $category['name'] ?></span>
                                        </div>
                                    </div>

                                    <!-- Content -->
                                    <div class="col-8 d-flex flex-column">
                                        <div class="card-body py-2 px-3 d-flex flex-column h-100">
                                            <h5 class="card-title h6 fw-bold mb-1 text-truncate"><?= $paper['title'] ?></h5>
                                            <p class="card-text small text-muted mb-2 flex-grow-1" style="line-height: 1.4;">
                                                <?= substr($paper['description'], 0, 80) . (strlen($paper['description']) > 80 ? '...' : '') ?>
                                            </p>
                                            <div class="d-flex justify-content-between align-items-center mt-auto">
                                                <span class="fw-bold text-primary">$<?= number_format($paper['price'], 2) ?></span>
                                                <a href="<?= base_url('papers/view/' . $paper['id']) ?>" class="btn btn-sm btn-outline-primary py-1 px-2">
                                                    View <i class="fas fa-arrow-right ms-1 small"></i>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?= $this->endSection() ?>