<?= $this->extend('templates/main_layout') ?>

<?= $this->section('content') ?>

<!-- Include PDF.js configuration -->
<script src="<?= base_url('public/assets/pdfjs/configuration.js') ?>"></script>
<!-- Include PDF.js library -->
<script src="<?= base_url('public/assets/pdfjs/build/pdf.js') ?>"></script>
<script src="<?= base_url('public/assets/pdfjs/build/pdf.worker.js') ?>"></script>

<!-- Paper Header -->
<div class="bg-light py-4 border-bottom">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-7">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb mb-2">
                        <li class="breadcrumb-item"><a href="<?= base_url() ?>" class="text-decoration-none">Home</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('papers') ?>" class="text-decoration-none">Papers</a></li>
                        <?php if (isset($paper['category_id']) && isset($paper['category_name'])): ?>
                            <li class="breadcrumb-item"><a href="<?= base_url('papers?category=' . $paper['category_id']) ?>" class="text-decoration-none"><?= $paper['category_name'] ?></a></li>
                        <?php endif; ?>
                        <li class="breadcrumb-item active" aria-current="page"><?= $paper['title'] ?></li>
                    </ol>
                </nav>
                <h1 class="h3 fw-bold mb-0"><?= $paper['title'] ?></h1>
            </div>
            <div class="col-md-5 text-md-end mt-3 mt-md-0">
                <?php if (isset($paper['category_name'])): ?>
                    <span class="badge bg-primary rounded-pill px-3 py-2 me-2"><?= $paper['category_name'] ?></span>
                <?php endif; ?>
                <span class="text-muted small"><i class="far fa-calendar-alt me-1"></i> <?= date('M d, Y', strtotime($paper['created_at'])) ?></span>
            </div>
        </div>
    </div>
</div>

<div class="container py-5">
    <div class="row">
        <!-- Main Content -->
        <div class="col-lg-8 pe-lg-5">
            <!-- Paper Image and Details -->
            <div class="card border-0 shadow-sm mb-4 overflow-hidden">
                <div class="row g-0">
                    <div class="col-md-4">
                        <?php if (!empty($paper['preview_path'])): ?>
                            <img src="<?= base_url('uploads/papers/previews/' . $paper['preview_path']) ?>"
                                alt="<?= $paper['title'] ?>"
                                class="img-fluid h-100 w-100"
                                style="object-fit: cover; min-height: 250px;">
                        <?php else: ?>
                            <img src="https://images.unsplash.com/photo-1532012197267-da84d127e765?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80"
                                alt="<?= $paper['title'] ?>"
                                class="img-fluid h-100 w-100"
                                style="object-fit: cover; min-height: 250px;">
                        <?php endif; ?>
                    </div>
                    <div class="col-md-8">
                        <div class="card-body p-4">
                            <div class="d-flex justify-content-between align-items-start mb-3">
                                <h2 class="h4 fw-bold mb-0"><?= $paper['title'] ?></h2>
                                <span class="badge bg-success rounded-pill">Available</span>
                            </div>

                            <div class="mb-3">
                                <?php if (isset($paper['category_name'])): ?>
                                    <div class="d-flex align-items-center text-muted small mb-2">
                                        <i class="fas fa-folder me-2 text-primary"></i>
                                        <span><?= $paper['category_name'] ?></span>
                                    </div>
                                <?php endif; ?>
                                <div class="d-flex align-items-center text-muted small mb-2">
                                    <i class="fas fa-calendar-alt me-2 text-primary"></i>
                                    <span>Added on <?= date('F j, Y', strtotime($paper['created_at'])) ?></span>
                                </div>
                                <!--<div class="d-flex align-items-center text-muted small">
                                    <i class="fas fa-file-alt me-2 text-primary"></i>
                                    <span><? //= rand(5, 20) 
                                            ?> pages</span>
                                </div>--->
                            </div>

                            <div class="d-flex align-items-center mt-4">
                                <h3 class="h4 text-primary fw-bold mb-0">$<?= number_format($paper['price'], 2) ?></h3>
                                <form action="<?= base_url('cart/add') ?>" method="post" class="ms-auto">
                                    <?= csrf_field() ?>
                                    <input type="hidden" name="paper_id" value="<?= $paper['id'] ?>">
                                    <button type="submit" class="btn btn-primary px-4">
                                        <i class="fas fa-shopping-cart me-2"></i>Add to Cart
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Paper Description -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white py-3">
                    <h3 class="h5 fw-bold mb-0">
                        <i class="fas fa-info-circle text-primary me-2"></i>Description
                    </h3>
                </div>
                <div class="card-body p-4">
                    <div class="paper-description">
                        <?= nl2br($paper['description']) ?>
                    </div>
                </div>
            </div>

            <!-- Paper Preview -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white py-3">
                    <h3 class="h5 fw-bold mb-0">
                        <i class="fas fa-eye text-primary me-2"></i>Preview
                    </h3>
                </div>
                <div class="card-body p-4">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>This is a preview of the paper. Purchase to access the full content.
                    </div>

                    <div class="paper-preview bg-light p-4 rounded text-center">
                        <i class="fas fa-file-pdf text-danger fa-3x mb-3"></i>
                        <h4 class="h6 fw-bold"><?= $paper['title'] ?></h4>
                        <p class="text-muted small mb-3">
                            <?php if (isset($paper['category_name'])): ?>
                                <?= $paper['category_name'] ?> •
                            <?php endif; ?>
                            <?= rand(5, 20) ?> pages
                        </p>
                        <a href="#" class="btn btn-sm btn-outline-primary disabled">
                            <i class="fas fa-lock me-1"></i>Preview Locked
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Buy Now Card -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white py-3">
                    <h3 class="h5 fw-bold mb-0">
                        <i class="fas fa-shopping-cart text-primary me-2"></i>Purchase Options
                    </h3>
                </div>
                <div class="card-body p-4">
                    <div class="price-box bg-light p-4 rounded text-center mb-4">
                        <span class="text-muted small text-uppercase">Price</span>
                        <h3 class="display-6 fw-bold text-primary mb-0">₹<?= number_format($paper['price'], 2) ?></h3>
                    </div>

                    <form action="<?= base_url('cart/add') ?>" method="post">
                        <?= csrf_field() ?>
                        <input type="hidden" name="paper_id" value="<?= $paper['id'] ?>">
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary btn-lg add-to-cart">
                                <i class="fas fa-shopping-cart me-2"></i>Add to Cart
                            </button>
                            <button type="submit" class="btn btn-success btn-lg" formaction="<?= base_url('checkout/one-page') ?>">
                                <i class="fas fa-bolt me-2"></i>Buy Now
                            </button>
                        </div>
                    </form>

                    <div class="mt-4">
                        <div class="d-flex align-items-center mb-3">
                            <div class="d-flex align-items-center justify-content-center bg-primary bg-opacity-10 rounded-circle me-3" style="width: 36px; height: 36px;">
                                <i class="fas fa-lock text-primary"></i>
                            </div>
                            <span class="small">Secure Payment</span>
                        </div>
                        <div class="d-flex align-items-center mb-3">
                            <div class="d-flex align-items-center justify-content-center bg-primary bg-opacity-10 rounded-circle me-3" style="width: 36px; height: 36px;">
                                <i class="fas fa-download text-primary"></i>
                            </div>
                            <span class="small">Instant Download</span>
                        </div>
                        <div class="d-flex align-items-center">
                            <div class="d-flex align-items-center justify-content-center bg-primary bg-opacity-10 rounded-circle me-3" style="width: 36px; height: 36px;">
                                <i class="fas fa-headset text-primary"></i>
                            </div>
                            <span class="small">24/7 Customer Support</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Related Papers -->
            <?php if (!empty($relatedPapers)): ?>
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-white py-3">
                        <h3 class="h5 fw-bold mb-0">
                            <i class="fas fa-link text-primary me-2"></i>Related Papers
                        </h3>
                    </div>
                    <div class="card-body p-0">
                        <ul class="list-group list-group-flush">
                            <?php foreach ($relatedPapers as $relatedPaper): ?>
                                <li class="list-group-item p-3">
                                    <div class="d-flex">
                                        <div class="flex-shrink-0">
                                            <?php if (!empty($relatedPaper['preview_path'])): ?>
                                                <img src="<?= base_url('uploads/papers/previews/' . $relatedPaper['preview_path']) ?>"
                                                    alt="<?= $relatedPaper['title'] ?>"
                                                    class="rounded"
                                                    width="50" height="50"
                                                    style="object-fit: cover;">
                                            <?php else: ?>
                                                <div class="bg-light rounded d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                                                    <i class="fas fa-file-alt text-primary"></i>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                        <div class="ms-3">
                                            <a href="<?= base_url('papers/view/' . $relatedPaper['id']) ?>" class="text-decoration-none fw-medium">
                                                <?= $relatedPaper['title'] ?>
                                            </a>
                                            <div class="d-flex align-items-center mt-1">
                                                <?php if (isset($relatedPaper['category_name'])): ?>
                                                    <span class="badge bg-primary bg-opacity-10 text-primary me-2"><?= $relatedPaper['category_name'] ?></span>
                                                <?php endif; ?>
                                                <span class="text-primary fw-bold small">$<?= number_format($relatedPaper['price'], 2) ?></span>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                    <div class="card-footer bg-white text-center p-3">
                        <a href="<?= base_url('papers?category=' . (isset($paper['category_id']) ? $paper['category_id'] : '')) ?>" class="btn btn-sm btn-outline-primary">
                            View More Papers
                            <?php if (isset($paper['category_name'])): ?>
                                in <?= $paper['category_name'] ?>
                            <?php endif; ?>
                        </a>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?= $this->endSection() ?>