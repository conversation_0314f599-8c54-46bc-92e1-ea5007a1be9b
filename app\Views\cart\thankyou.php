<?= $this->extend('templates/main_layout') ?>

<?= $this->section('content') ?>
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card mb-4">
                <div class="card-body text-center py-5">
                    <i class="fas fa-check-circle text-success" style="font-size: 5rem;"></i>
                    <h1 class="mt-4">Thank You for Your Order!</h1>
                    <p class="lead">Your order has been placed successfully.</p>
                    <p>Order #<?= $order['id'] ?></p>

                    <div class="alert alert-info mt-4">
                        <p>A confirmation email has been sent to your email address.</p>
                        <p>You can view your order details in your <a href="<?= base_url('profile/orders') ?>">order history</a>.</p>
                    </div>

                    <?php if ($order['payment_status'] === 'pending'): ?>
                        <div class="alert alert-warning mt-4">
                            <p><strong>Payment Status:</strong> Pending</p>
                            <p>Your payment is being processed. You will be able to access your purchased papers once the payment is confirmed.</p>
                        </div>
                    <?php elseif ($order['payment_status'] === 'completed'): ?>
                        <div class="alert alert-success mt-4">
                            <p><strong>Payment Status:</strong> Completed</p>
                            <p>Your payment has been confirmed. Transaction ID: <?= $order['transaction_id'] ?></p>
                            <p>You can now access your purchased papers in your <a href="<?= base_url('profile/downloads') ?>">downloads</a>.</p>
                        </div>
                    <?php elseif ($order['payment_status'] === 'failed'): ?>
                        <div class="alert alert-danger mt-4">
                            <p><strong>Payment Status:</strong> Failed</p>
                            <p>Your payment could not be processed. Please try again or contact customer support.</p>
                        </div>
                    <?php endif; ?>
                </div>
                <div class="card-footer">
                    <div class="d-flex justify-content-between">
                        <a href="<?= base_url() ?>" class="btn btn-primary">
                            <i class="fas fa-home me-2"></i>Return to Home
                        </a>
                        <a href="<?= base_url('profile/orders') ?>" class="btn btn-outline-primary">
                            <i class="fas fa-list me-2"></i>View Orders
                        </a>
                    </div>
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Order Summary</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Item</th>
                                    <th>Price</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($orderItems as $item): ?>
                                    <tr>
                                        <td><?= esc($item['paper_title']) ?></td>
                                        <td><?= number_format($item['price'], 2) ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                            <tfoot>
                                <tr>
                                    <th colspan="1" class="text-end">Total:</th>
                                    <th><?= number_format($order['total_amount'], 2) ?></th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>
