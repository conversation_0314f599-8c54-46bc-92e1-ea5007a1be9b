<?php

namespace App\Models;

use CodeIgniter\Model;

class PaperModel extends Model
{
    protected $table = 'papers';
    protected $primaryKey = 'id';
    protected $allowedFields = ['title', 'description', 'category_id', 'price', 'file_path', 'preview_path', 'status'];
    protected $useTimestamps = true;
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $returnType = 'array';

    // Add this method to get latest papers with their categories
    public function getLatestPapersWithCategories($limit = 10)
    {
        return $this->select('papers.*, categories.name as category_name')
                    ->join('categories', 'categories.id = papers.category_id')
                    ->where('papers.status', 'active')
                    ->orderBy('papers.created_at', 'DESC')
                    ->limit($limit)
                    ->find();
    }

    // You might also need these related methods
    public function getPapersByCategory($categoryId, $limit = 10)
    {
        return $this->select('papers.*, categories.name as category_name')
                    ->join('categories', 'categories.id = papers.category_id')
                    ->where('papers.category_id', $categoryId)
                    ->where('papers.status', 'active')
                    ->orderBy('papers.created_at', 'DESC')
                    ->limit($limit)
                    ->find();
    }

    public function getPaperWithCategory($paperId)
    {
        return $this->select('papers.*, categories.name as category_name')
                    ->join('categories', 'categories.id = papers.category_id')
                    ->where('papers.id', $paperId)
                    ->first();
    }

    public function getFeaturedPapers($limit = 6)
    {
        return $this->select('papers.*, categories.name as category_name')
                    ->join('categories', 'categories.id = papers.category_id')
                    ->where('papers.featured', 1)
                    ->where('papers.status', 'active')
                    ->orderBy('papers.created_at', 'DESC')
                    ->limit($limit)
                    ->find();
    }

    public function searchPapers($search = null, $filters = [])
    {
        $builder = $this->select('papers.*, categories.name as category_name')
                        ->join('categories', 'categories.id = papers.category_id')
                        ->where('papers.status', 'active');

        // Apply search term if provided
        if (!empty($search)) {
            $builder->groupStart()
                    ->like('papers.title', $search)
                    ->orLike('papers.description', $search)
                    ->orLike('categories.name', $search)
                    ->groupEnd();
        }

        // Apply category filter if provided
        if (!empty($filters['category_id'])) {
            $builder->where('papers.category_id', $filters['category_id']);
        }

        // Apply price range filters if provided
        if (!empty($filters['min_price'])) {
            $builder->where('papers.price >=', $filters['min_price']);
        }

        if (!empty($filters['max_price'])) {
            $builder->where('papers.price <=', $filters['max_price']);
        }

        // Order by newest first
        $builder->orderBy('papers.created_at', 'DESC');

        return $builder->findAll();
    }
}