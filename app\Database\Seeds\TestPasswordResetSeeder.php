<?php

namespace App\Database\Seeds;

use CodeIgniter\Database\Seeder;

class TestPasswordResetSeeder extends Seeder
{
    public function run()
    {
        $data = [
            'email' => '<EMAIL>',
            'token' => '735c41f26fc8b69e51bf12e6d2a2cbbbb87a45359ef6673eaec8fbff5c555007',
            'created_at' => date('Y-m-d H:i:s'),
            'expires_at' => date('Y-m-d H:i:s', time() + 3600) // 1 hour from now
        ];

        $this->db->table('password_resets')->insert($data);
    }
}
