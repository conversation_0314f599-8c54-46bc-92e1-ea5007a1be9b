<?php

namespace App\Controllers\Api;

use App\Controllers\BaseController;
use App\Models\UserModel;
use CodeIgniter\API\ResponseTrait;

class AuthController extends BaseController
{
    use ResponseTrait;

    protected $userModel;

    public function __construct()
    {
        $this->userModel = new UserModel();
        helper('jwt');
    }

    /**
     * Handle user login and return JWT token
     *
     * @return \CodeIgniter\HTTP\Response
     */
    public function login()
    {
        // Log request information
        log_message('debug', 'API Login Request - Method: ' . $this->request->getMethod());
        log_message('debug', 'API Login Request - Headers: ' . json_encode($this->request->headers()));
        log_message('debug', 'API Login Request - Body: ' . json_encode($this->request->getBody()));

        // Get raw input
        $rawInput = $this->request->getBody();
        log_message('debug', 'Raw request body: ' . $rawInput);

        $rules = [
            'email' => 'required|valid_email',
            'password' => 'required|min_length[6]'
        ];

        // Get input data from either POST or JSON request
        $input = $this->request->getJSON(true);
        log_message('debug', 'JSON input: ' . json_encode($input));

        if (empty($input)) {
            $input = $this->request->getPost();
            log_message('debug', 'POST input: ' . json_encode($input));
        }

        // If still empty, try to parse raw input
        if (empty($input) && !empty($rawInput)) {
            try {
                $input = json_decode($rawInput, true);
                log_message('debug', 'Parsed raw input: ' . json_encode($input));
            } catch (\Exception $e) {
                log_message('error', 'Failed to parse raw input: ' . $e->getMessage());
            }
        }

        // Log the final input
        log_message('debug', 'Final input for validation: ' . json_encode($input));

        // Validate input
        if (!$this->validate($rules, $input)) {
            log_message('error', 'Validation failed: ' . json_encode($this->validator->getErrors()));
            return $this->failValidationErrors($this->validator->getErrors());
        }

        $email = $input['email'] ?? '';
        $password = $input['password'] ?? '';

        log_message('debug', 'Attempting login for email: ' . $email);

        $user = $this->userModel->getUserByEmail($email);

        if (!$user) {
            log_message('error', 'User not found with email: ' . $email);
            return $this->failNotFound('User not found with the provided email.');
        }

        log_message('debug', 'User found: ' . json_encode($user));

        if (!password_verify($password, $user['password'])) {
            log_message('error', 'Invalid password for user: ' . $email);
            return $this->fail('Invalid password.', 401);
        }

        // Check if user is active
        if ($user['status'] !== 'active') {
            log_message('error', 'User account not active: ' . $email);
            return $this->fail('Your account is not active. Please contact support.', 403);
        }

        // Check if user is admin (we only want to allow frontend users)
        if ($user['is_admin'] === 'admin') {
            log_message('error', 'Admin user attempted API login: ' . $email);
            return $this->fail('Admin users cannot use the API. Please use the web interface.', 403);
        }

        // Generate JWT token
        $token = generateJWTToken($user);
        log_message('debug', 'Generated JWT token for user: ' . $email);

        // Prepare response
        $response = [
            'status' => true,
            'message' => 'Login successful',
            'token' => $token,
            'user' => [
                'id' => $user['id'],
                'name' => $user['name'],
                'email' => $user['email'],
                'profile_image' => $user['profile_image'] ? (strpos($user['profile_image'], 'uploads/profiles/') === 0 ? base_url($user['profile_image']) : base_url('uploads/profiles/' . $user['profile_image'])) : null
            ]
        ];

        log_message('debug', 'Login successful for user: ' . $email);
        log_message('debug', 'Response: ' . json_encode($response));

        // Return token and user data
        return $this->respond($response);
    }

    /**
     * Handle user registration
     *
     * @return \CodeIgniter\HTTP\Response
     */
    public function register()
    {
        $rules = [
            'name' => 'required|min_length[3]|max_length[50]',
            'email' => 'required|valid_email|is_unique[users.email]',
            'password' => 'required|min_length[6]',
            'confirm_password' => 'required|matches[password]'
        ];

        if (!$this->validate($rules)) {
            return $this->failValidationErrors($this->validator->getErrors());
        }

        // Get input data from either POST or JSON request
        $input = $this->request->getJSON(true);
        if (empty($input)) {
            $input = $this->request->getPost();
        }

        $userData = [
            'name' => $input['name'] ?? '',
            'email' => $input['email'] ?? '',
            'password' => password_hash($input['password'] ?? '', PASSWORD_DEFAULT),
            'is_admin' => 'user',
            'status' => 'active',
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        try {
            $userId = $this->userModel->insert($userData);

            if (!$userId) {
                return $this->fail('Registration failed. Please try again.', 500);
            }

            // Get the newly created user
            $user = $this->userModel->find($userId);

            // Generate JWT token
            $token = generateJWTToken($user);

            // Return token and user data
            return $this->respondCreated([
                'status' => true,
                'message' => 'Registration successful',
                'token' => $token,
                'user' => [
                    'id' => $user['id'],
                    'name' => $user['name'],
                    'email' => $user['email'],
                    'profile_image' => $user['profile_image'] ? (strpos($user['profile_image'], 'uploads/profiles/') === 0 ? base_url($user['profile_image']) : base_url('uploads/profiles/' . $user['profile_image'])) : null
                ]
            ]);
        } catch (\Exception $e) {
            log_message('error', 'Registration error: ' . $e->getMessage());
            return $this->fail('Registration failed: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Get current user profile
     *
     * @return \CodeIgniter\HTTP\Response
     */
    public function profile()
    {
        // The user data is set by the JWT filter
        $userData = $this->request->jwtUser ?? null;

        if (!$userData) {
            return $this->failUnauthorized('Invalid or expired token. Please login again.');
        }

        // Get fresh user data from database
        $user = $this->userModel->find($userData['id']);

        if (!$user) {
            return $this->failNotFound('User not found.');
        }

        return $this->respond([
            'status' => true,
            'user' => [
                'id' => $user['id'],
                'name' => $user['name'],
                'email' => $user['email'],
                'profile_image' => $user['profile_image'] ? (strpos($user['profile_image'], 'uploads/profiles/') === 0 ? base_url($user['profile_image']) : base_url('uploads/profiles/' . $user['profile_image'])) : null,
                'created_at' => $user['created_at'],
                'updated_at' => $user['updated_at']
            ]
        ]);
    }

    /**
     * Refresh JWT token
     *
     * @return \CodeIgniter\HTTP\Response
     */
    public function refreshToken()
    {
        // The user data is set by the JWT filter
        $userData = $this->request->jwtUser ?? null;

        if (!$userData) {
            return $this->failUnauthorized('Invalid or expired token. Please login again.');
        }

        // Get fresh user data from database
        $user = $this->userModel->find($userData['id']);

        if (!$user) {
            return $this->failNotFound('User not found.');
        }

        // Generate new JWT token
        $token = generateJWTToken($user);

        return $this->respond([
            'status' => true,
            'message' => 'Token refreshed successfully',
            'token' => $token
        ]);
    }
}
