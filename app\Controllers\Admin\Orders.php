<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Models\OrderModel;
use App\Models\OrderItemModel;
use App\Models\UserModel;

class Orders extends BaseController
{
    protected $orderModel;
    protected $orderItemModel;
    protected $userModel;
    
    public function __construct()
    {
        $this->orderModel = new OrderModel();
        $this->orderItemModel = new OrderItemModel();
        $this->userModel = new UserModel();
    }
    
    public function index()
    {
        $data = [
            'title' => 'Orders Management',
            'orders' => $this->orderModel->select('orders.*, users.name as user_name, users.email as user_email')
                        ->join('users', 'users.id = orders.user_id')
                        ->orderBy('orders.created_at', 'DESC')
                        ->paginate(10),
            'pager' => $this->orderModel->pager
        ];
        
        return view('admin/orders/index', $data);
    }
    
    public function view($id = null)
    {
        if ($id === null) {
            return redirect()->to('/admin/orders')->with('error', 'Order ID is required.');
        }
        
        $order = $this->orderModel->select('orders.*, users.name as user_name, users.email as user_email')
                                 ->join('users', 'users.id = orders.user_id')
                                 ->where('orders.id', $id)
                                 ->first();
        
        if (!$order) {
            return redirect()->to('/admin/orders')->with('error', 'Order not found.');
        }
        
        $orderItems = $this->orderItemModel->getOrderItems($id);
        
        $data = [
            'title' => 'View Order #' . $id,
            'order' => $order,
            'orderItems' => $orderItems
        ];
        
        return view('admin/orders/view', $data);
    }
    
    public function updateStatus($id = null, $status = null)
    {
        if ($id === null || $status === null) {
            return redirect()->to('/admin/orders')->with('error', 'Order ID and status are required.');
        }
        
        $allowedStatuses = ['pending', 'completed', 'failed'];
        
        if (!in_array($status, $allowedStatuses)) {
            return redirect()->to('/admin/orders')->with('error', 'Invalid status.');
        }
        
        $order = $this->orderModel->find($id);
        
        if (!$order) {
            return redirect()->to('/admin/orders')->with('error', 'Order not found.');
        }
        
        $this->orderModel->update($id, ['payment_status' => $status]);
        
        return redirect()->to('/admin/orders/view/' . $id)->with('success', 'Order status updated successfully.');
    }
}
