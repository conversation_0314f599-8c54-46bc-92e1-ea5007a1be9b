<?xml version="1.0" encoding="UTF-8"?>
<svg width="400px" height="200px" viewBox="0 0 400 200" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>Shopping Cart Illustration</title>
    <defs>
        <linearGradient x1="0%" y1="0%" x2="100%" y2="100%" id="linearGradient-1">
            <stop stop-color="#4361EE" offset="0%"></stop>
            <stop stop-color="#3A56D4" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="shopping-cart-illustration">
            <rect id="background" fill="#F8F9FA" x="0" y="0" width="400" height="200" rx="10"></rect>
            <g id="cart" transform="translate(150.000000, 50.000000)">
                <path d="M20,0 L80,0 L90,60 L10,60 L20,0 Z" id="cart-body" fill="url(#linearGradient-1)"></path>
                <circle id="wheel-left" fill="#FFFFFF" cx="30" cy="80" r="10"></circle>
                <circle id="wheel-right" fill="#FFFFFF" cx="70" cy="80" r="10"></circle>
                <line x1="30" y1="70" x2="30" y2="60" id="wheel-connector-left" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round"></line>
                <line x1="70" y1="70" x2="70" y2="60" id="wheel-connector-right" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round"></line>
                <circle id="wheel-inner-left" fill="#4361EE" cx="30" cy="80" r="3"></circle>
                <circle id="wheel-inner-right" fill="#4361EE" cx="70" cy="80" r="3"></circle>
                <path d="M20,0 C20,0 30,20 50,20 C70,20 80,0 80,0" id="handle" stroke="#FFFFFF" stroke-width="3" stroke-linecap="round"></path>
            </g>
            <g id="paper-1" transform="translate(100.000000, 70.000000)">
                <rect id="paper-bg" fill="#FFFFFF" x="0" y="0" width="40" height="50" rx="5"></rect>
                <line x1="10" y1="10" x2="30" y2="10" id="line-1" stroke="#4361EE" stroke-width="2" stroke-linecap="round"></line>
                <line x1="10" y1="20" x2="30" y2="20" id="line-2" stroke="#4361EE" stroke-width="2" stroke-linecap="round"></line>
                <line x1="10" y1="30" x2="20" y2="30" id="line-3" stroke="#4361EE" stroke-width="2" stroke-linecap="round"></line>
            </g>
            <g id="paper-2" transform="translate(260.000000, 70.000000)">
                <rect id="paper-bg" fill="#FFFFFF" x="0" y="0" width="40" height="50" rx="5"></rect>
                <line x1="10" y1="10" x2="30" y2="10" id="line-1" stroke="#4361EE" stroke-width="2" stroke-linecap="round"></line>
                <line x1="10" y1="20" x2="30" y2="20" id="line-2" stroke="#4361EE" stroke-width="2" stroke-linecap="round"></line>
                <line x1="10" y1="30" x2="20" y2="30" id="line-3" stroke="#4361EE" stroke-width="2" stroke-linecap="round"></line>
            </g>
            <g id="paper-3" transform="translate(180.000000, 30.000000)">
                <rect id="paper-bg" fill="#FFFFFF" x="0" y="0" width="40" height="50" rx="5"></rect>
                <line x1="10" y1="10" x2="30" y2="10" id="line-1" stroke="#4361EE" stroke-width="2" stroke-linecap="round"></line>
                <line x1="10" y1="20" x2="30" y2="20" id="line-2" stroke="#4361EE" stroke-width="2" stroke-linecap="round"></line>
                <line x1="10" y1="30" x2="20" y2="30" id="line-3" stroke="#4361EE" stroke-width="2" stroke-linecap="round"></line>
            </g>
            <g id="plus" transform="translate(50.000000, 50.000000)">
                <circle id="plus-bg" fill="#4361EE" opacity="0.2" cx="20" cy="20" r="20"></circle>
                <line x1="10" y1="20" x2="30" y2="20" id="horizontal" stroke="#4361EE" stroke-width="2" stroke-linecap="round"></line>
                <line x1="20" y1="10" x2="20" y2="30" id="vertical" stroke="#4361EE" stroke-width="2" stroke-linecap="round"></line>
            </g>
            <g id="check" transform="translate(330.000000, 50.000000)">
                <circle id="check-bg" fill="#4361EE" opacity="0.2" cx="20" cy="20" r="20"></circle>
                <polyline id="check-mark" stroke="#4361EE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" points="10 20 17 27 30 14"></polyline>
            </g>
        </g>
    </g>
</svg>
