# Sample Paper Store

A web application for browsing, purchasing, and downloading academic sample papers. Built with CodeIgniter 4.

## Table of Contents

- [Overview](#overview)
- [Features](#features)
- [Project Structure](#project-structure)
- [Installation](#installation)
- [Configuration](#configuration)
- [Authentication](#authentication)
- [Database](#database)
- [Key Components](#key-components)
- [Common Tasks](#common-tasks)
- [Troubleshooting](#troubleshooting)

## Overview

Sample Paper Store is a web application that allows users to browse, purchase, and download academic sample papers. The application includes user authentication, paper categorization, search functionality, and an admin panel for managing papers and users.

## Features

- **User Authentication**
  - Registration and login
  - Google OAuth integration
  - Password reset functionality
  - User profiles

- **Paper Management**
  - Browse papers by category
  - Search papers by title, author, or keywords
  - Filter papers by price range
  - Preview and download papers

- **Admin Panel**
  - Manage papers (add, edit, delete)
  - Manage categories
  - Manage users
  - View sales reports

- **Additional Pages**
  - Home page with featured papers
  - About page
  - Contact page
  - FAQ page

## Project Structure

The application follows the CodeIgniter 4 directory structure:

```
sample-paper/
├── app/                    # Application code
│   ├── Config/             # Configuration files
│   ├── Controllers/        # Controller classes
│   ├── Database/           # Database migrations and seeds
│   ├── Filters/            # HTTP filters
│   ├── Helpers/            # Helper functions
│   ├── Libraries/          # Custom libraries
│   ├── Models/             # Model classes
│   └── Views/              # View templates
├── public/                 # Publicly accessible files
│   ├── assets/             # CSS, JS, images
│   │   ├── css/            # CSS files
│   │   ├── js/             # JavaScript files
│   │   └── images/         # Image files
│   ├── papers/             # Uploaded paper files
│   └── index.php           # Entry point
├── writable/               # Writable directory for logs, cache, etc.
├── vendor/                 # Composer dependencies
└── README.md               # This file
```

### Key Directories

- **Controllers (app/Controllers/)**: Contains all controller classes that handle HTTP requests.
- **Models (app/Models/)**: Contains model classes for database interaction.
- **Views (app/Views/)**: Contains view templates organized by feature.
- **Config (app/Config/)**: Contains configuration files for the application.
- **Libraries (app/Libraries/)**: Contains custom libraries like GoogleClient and EmailService.

## Installation

1. **Clone the repository**:
   ```bash
   git clone https://github.com/yourusername/sample-paper.git
   cd sample-paper
   ```

2. **Install dependencies**:
   ```bash
   composer install
   ```

3. **Set up the database**:
   - Create a MySQL database
   - Update the database configuration in `app/Config/Database.php`
   - Run migrations:
     ```bash
     php spark migrate
     ```
   - (Optional) Seed the database with sample data:
     ```bash
     php spark db:seed InitialSeeder
     ```

4. **Configure environment**:
   - Copy `env` to `.env`
   - Update the necessary environment variables

5. **Start the development server**:
   ```bash
   php spark serve
   ```

## Configuration

### Database Configuration

Update the database settings in `app/Config/Database.php`:

```php
public $default = [
    'DSN'      => '',
    'hostname' => 'localhost',
    'username' => 'your_username',
    'password' => 'your_password',
    'database' => 'sample_paper',
    'DBDriver' => 'MySQLi',
    // ...
];
```

### Google OAuth Configuration

To enable Google login, update the Google configuration in `app/Config/Google.php`:

```php
public $clientId = 'your-client-id.apps.googleusercontent.com';
public $clientSecret = 'your-client-secret';
public $redirectUri = 'auth/google/callback';
public $adminRedirectUri = 'admin/auth/google/callback';
```

### Email Configuration

For password reset functionality, update the email settings in `app/Config/Email.php`:

```php
public string $fromEmail = '<EMAIL>';
public string $fromName = 'Sample Paper Store';
public string $protocol = 'smtp';
public string $SMTPHost = 'your-smtp-host';
public string $SMTPUser = 'your-smtp-username';
public string $SMTPPass = 'your-smtp-password';
public int $SMTPPort = 587;
```

## Authentication

The authentication system is implemented in `app/Controllers/Auth.php` and includes:

- User registration
- User login
- Google OAuth integration
- Password reset functionality
- User profile management

### Google OAuth Integration

Google OAuth is implemented using the `GoogleClient` library in `app/Libraries/GoogleClient.php`. To use Google login:

1. Configure Google API credentials in Google Cloud Console
2. Update the Google configuration in `app/Config/Google.php`
3. Ensure the redirect URIs are properly set up in Google Cloud Console

### Password Reset

Password reset functionality is implemented in `app/Controllers/Auth.php` and uses:

- `PasswordResetModel` for storing reset tokens
- `EmailService` for sending reset emails
- Rate limiting to prevent abuse

## Database

### Database Schema

The main tables in the database are:

- **users**: Stores user information
- **categories**: Stores paper categories
- **papers**: Stores paper information
- **orders**: Stores order information
- **order_items**: Stores items in each order
- **password_resets**: Stores password reset tokens
- **password_reset_attempts**: Tracks password reset attempts for rate limiting

### Models

The main models are:

- **UserModel**: Handles user data
- **CategoryModel**: Handles category data
- **PaperModel**: Handles paper data
- **OrderModel**: Handles order data
- **PasswordResetModel**: Handles password reset tokens

## Key Components

### Controllers

- **Home**: Handles the home page and static pages (about, contact, FAQ)
- **Auth**: Handles user authentication
- **Papers**: Handles paper browsing and viewing
- **Categories**: Handles category browsing
- **Profile**: Handles user profile management
- **Admin**: Handles admin panel functionality

### Libraries

- **GoogleClient**: Handles Google OAuth integration
- **EmailService**: Handles email sending for password reset

### Views

Views are organized by feature:

- **auth/**: Authentication views (login, register, forgot password)
- **papers/**: Paper browsing and viewing
- **categories/**: Category browsing
- **profile/**: User profile management
- **admin/**: Admin panel views
- **templates/**: Layout templates

## Common Tasks

### Adding a New Page

1. Create a new method in the appropriate controller
2. Create a new view file in the appropriate directory
3. Add a route in `app/Config/Routes.php`

Example:

```php
// Controller method
public function newPage()
{
    $data = [
        'title' => 'New Page - Sample Paper Store'
    ];
    
    return view('new_page', $data);
}

// Route
$routes->get('new-page', 'Home::newPage');
```

### Adding a New Model

1. Create a new model file in `app/Models/`
2. Extend the `CodeIgniter\Model` class
3. Define the table, primary key, and allowed fields

Example:

```php
namespace App\Models;

use CodeIgniter\Model;

class NewModel extends Model
{
    protected $table = 'new_table';
    protected $primaryKey = 'id';
    protected $allowedFields = ['field1', 'field2', 'field3'];
    protected $useTimestamps = true;
    protected $returnType = 'array';
}
```

### Adding a New Migration

1. Create a new migration file in `app/Database/Migrations/`
2. Define the `up()` and `down()` methods

Example:

```php
namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateNewTable extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'field1' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
            ],
            'field2' => [
                'type' => 'TEXT',
                'null' => true,
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'updated_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
        ]);
        $this->forge->addKey('id', true);
        $this->forge->createTable('new_table');
    }

    public function down()
    {
        $this->forge->dropTable('new_table');
    }
}
```

## Troubleshooting

### Common Issues

#### Email Sending Issues

If you encounter issues with email sending:

1. Check the email configuration in `app/Config/Email.php`
2. Ensure the "From" header is properly set
3. For development, use the development mode in `EmailService` which displays the reset link directly

#### Google Login Issues

If Google login is not working:

1. Check the Google configuration in `app/Config/Google.php`
2. Ensure the redirect URIs are properly set up in Google Cloud Console
3. Check the browser console for any JavaScript errors

#### Database Connection Issues

If you encounter database connection issues:

1. Check the database configuration in `app/Config/Database.php`
2. Ensure the database server is running
3. Check the database credentials

### Logging

Logs are stored in `writable/logs/`. Check these logs for any errors or issues.

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Contact

For any questions or issues, please contact [<EMAIL>](mailto:<EMAIL>).
