<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;

class Settings extends BaseController
{
    public function index()
    {
        $data = [
            'title' => 'Site Settings'
        ];
        
        // Load current settings from a config file or database
        // For simplicity, we'll use a config file approach
        $settingsFile = WRITEPATH . 'settings.json';
        
        if (file_exists($settingsFile)) {
            $settings = json_decode(file_get_contents($settingsFile), true);
            $data['settings'] = $settings;
        } else {
            // Default settings
            $data['settings'] = [
                'site_name' => 'Sample Paper Store',
                'site_description' => 'Your one-stop destination for high-quality sample papers',
                'contact_email' => '<EMAIL>',
                'contact_phone' => '+1234567890',
                'address' => '123 Main St, City, Country',
                'currency' => 'INR',
                'tax_rate' => 0,
                'enable_registration' => true,
                'enable_guest_checkout' => false,
                'maintenance_mode' => false
            ];
        }
        
        return view('admin/settings/index', $data);
    }
    
    public function update()
    {
        // Validate and save settings
        $rules = [
            'site_name' => 'required|min_length[3]|max_length[255]',
            'site_description' => 'required',
            'contact_email' => 'required|valid_email',
            'contact_phone' => 'required',
            'address' => 'required',
            'currency' => 'required',
            'tax_rate' => 'required|numeric'
        ];
        
        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }
        
        $settings = [
            'site_name' => $this->request->getPost('site_name'),
            'site_description' => $this->request->getPost('site_description'),
            'contact_email' => $this->request->getPost('contact_email'),
            'contact_phone' => $this->request->getPost('contact_phone'),
            'address' => $this->request->getPost('address'),
            'currency' => $this->request->getPost('currency'),
            'tax_rate' => $this->request->getPost('tax_rate'),
            'enable_registration' => (bool)$this->request->getPost('enable_registration'),
            'enable_guest_checkout' => (bool)$this->request->getPost('enable_guest_checkout'),
            'maintenance_mode' => (bool)$this->request->getPost('maintenance_mode')
        ];
        
        // Save settings to file
        $settingsFile = WRITEPATH . 'settings.json';
        file_put_contents($settingsFile, json_encode($settings, JSON_PRETTY_PRINT));
        
        return redirect()->to('/admin/settings')->with('success', 'Settings updated successfully.');
    }
}
