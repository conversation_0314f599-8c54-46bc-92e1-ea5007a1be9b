<?php

namespace App\Controllers\Api;

use App\Controllers\BaseController;
use App\Models\UserModel;
use App\Models\OrderModel;
use App\Models\OrderItemModel as OrderItemModelAlias;
use CodeIgniter\API\ResponseTrait;

class UserController extends BaseController
{
    use ResponseTrait;

    protected $userModel;
    protected $orderModel;
    protected $orderItemModel;

    public function __construct()
    {
        $this->userModel = new UserModel();
        $this->orderModel = new OrderModel();
        $this->orderItemModel = new OrderItemModelAlias();
    }

    /**
     * Update user profile
     *
     * @return \CodeIgniter\HTTP\Response
     */
    public function updateProfile()
    {
        // The user data is set by the JWT filter
        $userData = $this->request->jwtUser ?? null;

        if (!$userData) {
            return $this->failUnauthorized('Invalid or expired token. Please login again.');
        }

        $rules = [
            'name' => 'required|min_length[3]|max_length[50]',
            'email' => 'required|valid_email'
        ];

        // Check if email is changed
        $user = $this->userModel->find($userData['id']);
        if ($user['email'] !== $this->request->getPost('email')) {
            $rules['email'] .= '|is_unique[users.email]';
        }

        if (!$this->validate($rules)) {
            return $this->failValidationErrors($this->validator->getErrors());
        }

        $updateData = [
            'name' => $this->request->getPost('name'),
            'email' => $this->request->getPost('email'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        try {
            $this->userModel->update($userData['id'], $updateData);

            // Get updated user data
            $updatedUser = $this->userModel->find($userData['id']);

            return $this->respond([
                'status' => true,
                'message' => 'Profile updated successfully',
                'user' => [
                    'id' => $updatedUser['id'],
                    'name' => $updatedUser['name'],
                    'email' => $updatedUser['email'],
                    'profile_image' => $updatedUser['profile_image'] ? (strpos($updatedUser['profile_image'], 'uploads/profiles/') === 0 ? base_url($updatedUser['profile_image']) : base_url('uploads/profiles/' . $updatedUser['profile_image'])) : null,
                    'created_at' => $updatedUser['created_at'],
                    'updated_at' => $updatedUser['updated_at']
                ]
            ]);
        } catch (\Exception $e) {
            log_message('error', 'Profile update error: ' . $e->getMessage());
            return $this->fail('Profile update failed: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Change user password
     *
     * @return \CodeIgniter\HTTP\Response
     */
    public function changePassword()
    {
        // The user data is set by the JWT filter
        $userData = $this->request->jwtUser ?? null;

        if (!$userData) {
            return $this->failUnauthorized('Invalid or expired token. Please login again.');
        }

        $rules = [
            'current_password' => 'required',
            'new_password' => 'required|min_length[6]',
            'confirm_password' => 'required|matches[new_password]'
        ];

        if (!$this->validate($rules)) {
            return $this->failValidationErrors($this->validator->getErrors());
        }

        $currentPassword = $this->request->getPost('current_password');
        $newPassword = $this->request->getPost('new_password');

        // Get user data
        $user = $this->userModel->find($userData['id']);

        // Verify current password
        if (!password_verify($currentPassword, $user['password'])) {
            return $this->fail('Current password is incorrect.', 400);
        }

        // Update password
        try {
            $this->userModel->update($userData['id'], [
                'password' => password_hash($newPassword, PASSWORD_DEFAULT),
                'updated_at' => date('Y-m-d H:i:s')
            ]);

            return $this->respond([
                'status' => true,
                'message' => 'Password changed successfully'
            ]);
        } catch (\Exception $e) {
            log_message('error', 'Password change error: ' . $e->getMessage());
            return $this->fail('Password change failed: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Get user orders
     *
     * @return \CodeIgniter\HTTP\Response
     */
    public function getOrders()
    {
        // The user data is set by the JWT filter
        $userData = $this->request->jwtUser ?? null;

        if (!$userData) {
            return $this->failUnauthorized('Invalid or expired token. Please login again.');
        }

        try {
            $orders = $this->orderModel->where('user_id', $userData['id'])
                ->orderBy('created_at', 'DESC')
                ->findAll();

            // Format orders data
            $formattedOrders = [];
            foreach ($orders as $order) {
                $orderItems = $this->orderItemModel->where('order_id', $order['id'])->findAll();

                $formattedOrders[] = [
                    'id' => $order['id'],
                    'total_amount' => $order['total_amount'],
                    'payment_status' => $order['payment_status'],
                    'payment_method' => $order['payment_method'],
                    'created_at' => $order['created_at'],
                    'updated_at' => $order['updated_at'],
                    'items_count' => count($orderItems)
                ];
            }

            return $this->respond([
                'status' => true,
                'orders' => $formattedOrders
            ]);
        } catch (\Exception $e) {
            log_message('error', 'Get orders error: ' . $e->getMessage());
            return $this->fail('Failed to retrieve orders: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Get order details
     *
     * @param int $orderId
     * @return \CodeIgniter\HTTP\Response
     */
    public function getOrderDetails($orderId)
    {
        // The user data is set by the JWT filter
        $userData = $this->request->jwtUser ?? null;

        if (!$userData) {
            return $this->failUnauthorized('Invalid or expired token. Please login again.');
        }

        try {
            // Get order
            $order = $this->orderModel->where('id', $orderId)
                ->where('user_id', $userData['id'])
                ->first();

            if (!$order) {
                return $this->failNotFound('Order not found or does not belong to you.');
            }

            // Get order items
            $orderItems = $this->orderItemModel->getOrderItems($orderId);

            return $this->respond([
                'status' => true,
                'order' => [
                    'id' => $order['id'],
                    'total_amount' => $order['total_amount'],
                    'payment_status' => $order['payment_status'],
                    'payment_method' => $order['payment_method'],
                    'created_at' => $order['created_at'],
                    'updated_at' => $order['updated_at'],
                    'items' => $orderItems
                ]
            ]);
        } catch (\Exception $e) {
            log_message('error', 'Get order details error: ' . $e->getMessage());
            return $this->fail('Failed to retrieve order details: ' . $e->getMessage(), 500);
        }
    }
}
