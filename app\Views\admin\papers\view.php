<?= $this->extend('templates/admin_layout') ?>

<?= $this->section('content') ?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 text-gray-800">View Paper Details</h1>
        <div>
            <a href="<?= base_url('admin/papers/edit/' . $paper['id']) ?>" class="btn btn-primary btn-sm">
                <i class="fas fa-edit"></i> Edit
            </a>
            <a href="<?= base_url('admin/papers') ?>" class="btn btn-secondary btn-sm">
                <i class="fas fa-arrow-left"></i> Back to List
            </a>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">Paper Information</h6>
                </div>
                <div class="card-body">
                    <table class="table table-bordered">
                        <tr>
                            <th style="width: 200px">Title</th>
                            <td><?= esc($paper['title']) ?></td>
                        </tr>
                        <tr>
                            <th>Category</th>
                            <td><?= esc($paper['category_name']) ?></td>
                        </tr>
                        <tr>
                            <th>Description</th>
                            <td><?= nl2br(esc($paper['description'])) ?></td>
                        </tr>
                        <tr>
                            <th>Price</th>
                            <td>$<?= number_format($paper['price'], 2) ?></td>
                        </tr>
                        <tr>
                            <th>Year</th>
                            <td><?= $paper['year'] ? $paper['year'] : '<span class="text-muted">Not specified</span>' ?></td>
                        </tr>
                        <tr>
                            <th>Status</th>
                            <td>
                                <?php if ($paper['status'] == 'active'): ?>
                                    <span class="badge badge-success">Active</span>
                                <?php else: ?>
                                    <span class="badge badge-danger">Inactive</span>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <tr>
                            <th>Created At</th>
                            <td><?= date('F j, Y g:i A', strtotime($paper['created_at'])) ?></td>
                        </tr>
                        <tr>
                            <th>Last Updated</th>
                            <td><?= date('F j, Y g:i A', strtotime($paper['updated_at'])) ?></td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">Files</h6>
                </div>
                <div class="card-body">
                    <div>
                        <h6 class="font-weight-bold">Preview</h6>
                        <?php if ($paper['file_path']): ?>
                            <iframe src="<?= base_url('uploads/papers/' . $paper['file_path']) ?>" style="width: 100%; height: 400px; border: 1px solid #dee2e6;"></iframe>
                        <?php else: ?>
                            <p class="text-muted">No preview available</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>


                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>