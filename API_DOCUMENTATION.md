# Sample Paper Store API Documentation

This document provides information about the REST API endpoints available for frontend users of the Sample Paper Store application.

## Authentication

The API uses <PERSON><PERSON><PERSON> (JSON Web Token) for authentication. To access protected endpoints, you need to include the <PERSON><PERSON><PERSON> token in the Authorization header of your requests.

```
Authorization: Bearer <your_jwt_token>
```

### Authentication Endpoints

#### Login

- **URL**: `/api/auth/login`
- **Method**: `POST`
- **Authentication**: None
- **Request Body**:
  ```json
  {
    "email": "<EMAIL>",
    "password": "your_password"
  }
  ```
- **Response**:
  ```json
  {
    "status": true,
    "message": "Login successful",
    "token": "your_jwt_token",
    "user": {
      "id": 1,
      "name": "<PERSON>",
      "email": "<EMAIL>",
      "profile_image": "https://example.com/profile.jpg"
    }
  }
  ```

#### Register

- **URL**: `/api/auth/register`
- **Method**: `POST`
- **Authentication**: None
- **Request Body**:
  ```json
  {
    "name": "<PERSON>",
    "email": "<EMAIL>",
    "password": "your_password",
    "confirm_password": "your_password"
  }
  ```
- **Response**:
  ```json
  {
    "status": true,
    "message": "Registration successful",
    "token": "your_jwt_token",
    "user": {
      "id": 1,
      "name": "<PERSON>e",
      "email": "<EMAIL>"
    }
  }
  ```

#### Get User Profile

- **URL**: `/api/auth/profile`
- **Method**: `GET`
- **Authentication**: Required
- **Response**:
  ```json
  {
    "status": true,
    "user": {
      "id": 1,
      "name": "John Doe",
      "email": "<EMAIL>",
      "profile_image": "https://example.com/profile.jpg",
      "created_at": "2023-01-01 12:00:00",
      "updated_at": "2023-01-01 12:00:00"
    }
  }
  ```

#### Refresh Token

- **URL**: `/api/auth/refresh-token`
- **Method**: `POST`
- **Authentication**: Required
- **Response**:
  ```json
  {
    "status": true,
    "message": "Token refreshed successfully",
    "token": "your_new_jwt_token"
  }
  ```

## User Management

### User Endpoints

#### Update Profile

- **URL**: `/api/user/update-profile`
- **Method**: `POST`
- **Authentication**: Required
- **Request Body**:
  ```json
  {
    "name": "John Doe Updated",
    "email": "<EMAIL>"
  }
  ```
- **Response**:
  ```json
  {
    "status": true,
    "message": "Profile updated successfully",
    "user": {
      "id": 1,
      "name": "John Doe Updated",
      "email": "<EMAIL>",
      "profile_image": "https://example.com/profile.jpg",
      "created_at": "2023-01-01 12:00:00",
      "updated_at": "2023-01-01 13:00:00"
    }
  }
  ```

#### Change Password

- **URL**: `/api/user/change-password`
- **Method**: `POST`
- **Authentication**: Required
- **Request Body**:
  ```json
  {
    "current_password": "your_current_password",
    "new_password": "your_new_password",
    "confirm_password": "your_new_password"
  }
  ```
- **Response**:
  ```json
  {
    "status": true,
    "message": "Password changed successfully"
  }
  ```

#### Get User Orders

- **URL**: `/api/user/orders`
- **Method**: `GET`
- **Authentication**: Required
- **Response**:
  ```json
  {
    "status": true,
    "orders": [
      {
        "id": 1,
        "total_amount": "50.00",
        "payment_status": "completed",
        "payment_method": "credit_card",
        "created_at": "2023-01-01 12:00:00",
        "updated_at": "2023-01-01 12:00:00",
        "items_count": 2
      }
    ]
  }
  ```

#### Get Order Details

- **URL**: `/api/user/orders/{order_id}`
- **Method**: `GET`
- **Authentication**: Required
- **Response**:
  ```json
  {
    "status": true,
    "order": {
      "id": 1,
      "total_amount": "50.00",
      "payment_status": "completed",
      "payment_method": "credit_card",
      "created_at": "2023-01-01 12:00:00",
      "updated_at": "2023-01-01 12:00:00",
      "items": [
        {
          "id": 1,
          "paper_id": 1,
          "title": "Sample Paper 1",
          "price": "25.00",
          "quantity": 1
        },
        {
          "id": 2,
          "paper_id": 2,
          "title": "Sample Paper 2",
          "price": "25.00",
          "quantity": 1
        }
      ]
    }
  }
  ```

## Papers

### Paper Endpoints

#### Get All Papers

- **URL**: `/api/papers`
- **Method**: `GET`
- **Authentication**: None
- **Response**:
  ```json
  {
    "status": true,
    "papers": [
      {
        "id": 1,
        "title": "Sample Paper 1",
        "description": "This is a sample paper",
        "category_id": 1,
        "category_name": "Mathematics",
        "price": "25.00",
        "preview_path": "https://example.com/preview.jpg",
        "created_at": "2023-01-01 12:00:00",
        "updated_at": "2023-01-01 12:00:00"
      }
    ]
  }
  ```

#### Get Paper Details

- **URL**: `/api/papers/{paper_id}`
- **Method**: `GET`
- **Authentication**: None
- **Response**:
  ```json
  {
    "status": true,
    "paper": {
      "id": 1,
      "title": "Sample Paper 1",
      "description": "This is a sample paper",
      "category_id": 1,
      "category_name": "Mathematics",
      "price": "25.00",
      "preview_path": "https://example.com/preview.jpg",
      "created_at": "2023-01-01 12:00:00",
      "updated_at": "2023-01-01 12:00:00"
    }
  }
  ```

#### Get Papers by Category

- **URL**: `/api/papers/category/{category_id}`
- **Method**: `GET`
- **Authentication**: None
- **Response**:
  ```json
  {
    "status": true,
    "category": {
      "id": 1,
      "name": "Mathematics",
      "description": "Mathematics papers"
    },
    "papers": [
      {
        "id": 1,
        "title": "Sample Paper 1",
        "description": "This is a sample paper",
        "category_id": 1,
        "category_name": "Mathematics",
        "price": "25.00",
        "preview_path": "https://example.com/preview.jpg",
        "created_at": "2023-01-01 12:00:00",
        "updated_at": "2023-01-01 12:00:00"
      }
    ]
  }
  ```

#### Search Papers

- **URL**: `/api/papers/search?keyword=math`
- **Method**: `GET`
- **Authentication**: None
- **Response**:
  ```json
  {
    "status": true,
    "keyword": "math",
    "papers": [
      {
        "id": 1,
        "title": "Sample Math Paper",
        "description": "This is a sample math paper",
        "category_id": 1,
        "category_name": "Mathematics",
        "price": "25.00",
        "preview_path": "https://example.com/preview.jpg",
        "created_at": "2023-01-01 12:00:00",
        "updated_at": "2023-01-01 12:00:00"
      }
    ]
  }
  ```

## Categories

### Category Endpoints

#### Get All Categories

- **URL**: `/api/categories`
- **Method**: `GET`
- **Authentication**: None
- **Response**:
  ```json
  {
    "status": true,
    "categories": [
      {
        "id": 1,
        "name": "Mathematics",
        "description": "Mathematics papers",
        "preview_image": "https://example.com/math.jpg"
      }
    ]
  }
  ```

#### Get Category Details

- **URL**: `/api/categories/{category_id}`
- **Method**: `GET`
- **Authentication**: None
- **Response**:
  ```json
  {
    "status": true,
    "category": {
      "id": 1,
      "name": "Mathematics",
      "description": "Mathematics papers",
      "preview_image": "https://example.com/math.jpg"
    }
  }
  ```

## Error Responses

All API endpoints return appropriate HTTP status codes and error messages in case of failure:

- **400 Bad Request**: Invalid input data
- **401 Unauthorized**: Missing or invalid authentication token
- **403 Forbidden**: Insufficient permissions
- **404 Not Found**: Resource not found
- **500 Internal Server Error**: Server-side error

Example error response:

```json
{
  "status": false,
  "message": "Error message describing the issue"
}
```

## CORS Support

The API supports Cross-Origin Resource Sharing (CORS) for frontend applications hosted on different domains.

## Rate Limiting

To prevent abuse, the API implements rate limiting. If you exceed the rate limit, you will receive a 429 Too Many Requests response.

## API Versioning

The current API version is v1. The API may be updated in the future with a new version number.

## Support

For any issues or questions regarding the API, <NAME_EMAIL>.
