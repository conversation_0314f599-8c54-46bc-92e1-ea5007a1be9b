<?php

if (!function_exists('safe_str_contains')) {
    /**
     * Safely check if a string contains another string
     * 
     * @param mixed $haystack The string to search in
     * @param string $needle The string to search for
     * @return bool True if $needle is found in $haystack, false otherwise
     */
    function safe_str_contains($haystack, $needle) {
        if (!is_string($haystack)) {
            // Convert to string or return false if not possible
            if (is_array($haystack)) {
                return false; // Arrays can't be converted to strings directly
            }
            $haystack = (string) $haystack;
        }
        
        return str_contains($haystack, $needle);
    }
}