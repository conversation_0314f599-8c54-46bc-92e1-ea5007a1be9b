<?= $this->extend('templates/admin_layout') ?>

<?= $this->section('content') ?>
<div class="container-fluid px-4">
    <h1 class="mt-4"><?= $title ?></h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="<?= base_url('admin/dashboard') ?>">Dashboard</a></li>
        <li class="breadcrumb-item active">Paper Reports</li>
    </ol>
    
    <div class="row">
        <div class="col-xl-6 col-md-6">
            <div class="card bg-primary text-white mb-4">
                <div class="card-body">
                    <h2 class="mb-0"><?= $totalPapers ?></h2>
                    <div>Total Papers</div>
                </div>
            </div>
        </div>
        <div class="col-xl-6 col-md-6">
            <div class="card bg-success text-white mb-4">
                <div class="card-body">
                    <h2 class="mb-0"><?= $activePapers ?></h2>
                    <div>Active Papers</div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-xl-12">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-chart-pie me-1"></i>
                    Papers by Category
                </div>
                <div class="card-body">
                    <canvas id="categoryChart" width="100%" height="40"></canvas>
                </div>
            </div>
        </div>
    </div>
    
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-table me-1"></i>
            Papers by Category
        </div>
        <div class="card-body">
            <table id="categoryTable" class="table table-striped">
                <thead>
                    <tr>
                        <th>Category</th>
                        <th>Number of Papers</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($categoryData as $data): ?>
                        <tr>
                            <td><?= esc($data['category_name']) ?></td>
                            <td><?= $data['paper_count'] ?></td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
    
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-table me-1"></i>
            Recently Added Papers
        </div>
        <div class="card-body">
            <table id="recentPapersTable" class="table table-striped">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Title</th>
                        <th>Price</th>
                        <th>Status</th>
                        <th>Created Date</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($recentPapers as $paper): ?>
                        <tr>
                            <td><?= $paper['id'] ?></td>
                            <td><?= esc($paper['title']) ?></td>
                            <td><?= number_format($paper['price'], 2) ?></td>
                            <td>
                                <?php if ($paper['status'] == 'active'): ?>
                                    <span class="badge bg-success">Active</span>
                                <?php else: ?>
                                    <span class="badge bg-danger">Inactive</span>
                                <?php endif; ?>
                            </td>
                            <td><?= date('M d, Y', strtotime($paper['created_at'])) ?></td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Category Chart
        const categoryData = <?= json_encode($categoryData) ?>;
        const categories = categoryData.map(item => item.category_name);
        const counts = categoryData.map(item => item.paper_count);
        
        const categoryCtx = document.getElementById('categoryChart').getContext('2d');
        new Chart(categoryCtx, {
            type: 'pie',
            data: {
                labels: categories,
                datasets: [{
                    data: counts,
                    backgroundColor: [
                        'rgba(255, 99, 132, 0.7)',
                        'rgba(54, 162, 235, 0.7)',
                        'rgba(255, 206, 86, 0.7)',
                        'rgba(75, 192, 192, 0.7)',
                        'rgba(153, 102, 255, 0.7)',
                        'rgba(255, 159, 64, 0.7)',
                        'rgba(199, 199, 199, 0.7)',
                        'rgba(83, 102, 255, 0.7)',
                        'rgba(40, 159, 64, 0.7)',
                        'rgba(210, 199, 199, 0.7)'
                    ],
                    borderWidth: 1
                }]
            }
        });
        
        // Initialize DataTables
        $('#categoryTable').DataTable({
            paging: true,
            searching: true,
            ordering: true,
            info: true
        });
        
        $('#recentPapersTable').DataTable({
            paging: true,
            searching: true,
            ordering: true,
            info: true
        });
    });
</script>
<?= $this->endSection() ?>
