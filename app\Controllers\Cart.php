<?php

namespace App\Controllers;

use App\Models\PaperModel;

class Cart extends BaseController
{
    protected $paperModel;

    public function __construct()
    {
        $this->paperModel = new PaperModel();
    }

    public function index()
    {
        $cart = session()->get('cart') ?? [];
        $items = [];
        $total = 0;

        foreach ($cart as $id => $quantity) {
            $paper = $this->paperModel->find($id);
            if ($paper) {
                $items[] = [
                    'id' => $id,
                    'title' => $paper['title'],
                    'price' => $paper['price'],
                    'preview_path' => $paper['preview_path']
                ];
                $total += $paper['price'];
            }
        }

        $data = [
            'title' => 'Shopping Cart',
            'items' => $items,
            'total' => $total
        ];

        return view('cart/index', $data);
    }

    public function add()
    {
        $id = $this->request->getPost('paper_id');
        $quantity = 1; // For digital products, quantity is typically 1

        if (!$id) {
            return redirect()->back()->with('error', 'Invalid paper ID.');
        }

        $paper = $this->paperModel->find($id);

        if (!$paper) {
            return redirect()->back()->with('error', 'Paper not found.');
        }

        // Get current cart
        $cart = session()->get('cart') ?? [];

        // Add item to cart
        if (isset($cart[$id])) {
            // Item already in cart, just update quantity
            $cart[$id] = $quantity;
        } else {
            // New item
            $cart[$id] = $quantity;
        }

        // Update session
        session()->set('cart', $cart);

        return redirect()->back()->with('success', 'Paper added to cart.');
    }

    public function remove($id = null)
    {
        if (!$id) {
            return redirect()->to('/cart')->with('error', 'Invalid paper ID.');
        }

        $cart = session()->get('cart') ?? [];

        if (isset($cart[$id])) {
            unset($cart[$id]);
            session()->set('cart', $cart);
        }

        return redirect()->to('/cart')->with('success', 'Item removed from cart.');
    }

    public function clear()
    {
        session()->remove('cart');
        return redirect()->to('/cart')->with('success', 'Cart cleared.');
    }

    public function checkout()
    {
        // Check if user is logged in
        if (!session()->get('isLoggedIn')) {
            return redirect()->to('/auth/login')->with('error', 'Please login to checkout.');
        }

        $cart = session()->get('cart') ?? [];

        if (empty($cart)) {
            return redirect()->to('/cart')->with('error', 'Your cart is empty.');
        }

        $items = [];
        $total = 0;

        foreach ($cart as $id => $quantity) {
            $paper = $this->paperModel->find($id);
            if ($paper) {
                $items[] = [
                    'id' => $id,
                    'title' => $paper['title'],
                    'price' => $paper['price']
                ];
                $total += $paper['price'];
            }
        }

        $data = [
            'title' => 'Checkout',
            'items' => $items,
            'total' => $total
        ];

        return view('cart/checkout', $data);
    }

    public function process()
    {
        // Check if user is logged in
        if (!session()->get('isLoggedIn')) {
            return redirect()->to('/auth/login')->with('error', 'Please login to checkout.');
        }

        $cart = session()->get('cart') ?? [];

        if (empty($cart)) {
            return redirect()->to('/cart')->with('error', 'Your cart is empty.');
        }

        // Validate payment information
        $rules = [
            'payment_method' => 'required|in_list[razorpay]'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        // Validate Razorpay payment ID if payment method is razorpay
        if ($this->request->getPost('payment_method') === 'razorpay') {
            if (!$this->request->getPost('razorpay_payment_id')) {
                // Log the error
                log_message('error', 'Razorpay payment failed: No payment ID received');
                log_message('error', 'POST data: ' . json_encode($this->request->getPost()));

                return redirect()->back()->with('error', 'Razorpay payment failed. Please try again.');
            }

            // Log successful payment ID
            log_message('info', 'Razorpay payment ID received: ' . $this->request->getPost('razorpay_payment_id'));
        }

        // Calculate total
        $total = 0;
        $items = [];

        foreach ($cart as $id => $quantity) {
            $paper = $this->paperModel->find($id);
            if ($paper) {
                $items[] = [
                    'id' => $id,
                    'price' => $paper['price']
                ];
                $total += $paper['price'];
            }
        }

        // Create order
        $orderModel = new \App\Models\OrderModel();
        $orderItemModel = new \App\Models\OrderItemModel();

        // For Razorpay, we'll set the payment status to 'completed' since payment is confirmed
        $paymentStatus = 'completed';
        $paymentId = $this->request->getPost('razorpay_payment_id');

        $orderData = [
            'user_id' => session()->get('id'),
            'total_amount' => $total,
            'payment_status' => $paymentStatus,
            'payment_method' => $this->request->getPost('payment_method'),
            'transaction_id' => $paymentId
        ];

        $orderId = $orderModel->insert($orderData);

        // Add order items
        foreach ($items as $item) {
            $orderItemModel->insert([
                'order_id' => $orderId,
                'paper_id' => $item['id'],
                'price' => $item['price']
            ]);
        }

        // Clear cart
        session()->remove('cart');

        // Redirect to thank you page
        return redirect()->to('/cart/thankyou/' . $orderId);
    }

    public function thankyou($orderId = null)
    {
        if (!$orderId) {
            return redirect()->to('/');
        }

        $orderModel = new \App\Models\OrderModel();
        $orderItemModel = new \App\Models\OrderItemModel();

        $order = $orderModel->find($orderId);

        if (!$order || $order['user_id'] != session()->get('id')) {
            return redirect()->to('/');
        }

        $orderItems = $orderItemModel->getOrderItems($orderId);

        $data = [
            'title' => 'Thank You',
            'order' => $order,
            'orderItems' => $orderItems
        ];

        return view('cart/thankyou', $data);
    }

    /**
     * Handle Razorpay webhook notifications
     */
    public function razorpayWebhook()
    {
        // Get the webhook data
        $input = file_get_contents('php://input');
        $webhook = json_decode($input, true);

        // Verify webhook signature
        $webhookSignature = $_SERVER['HTTP_X_RAZORPAY_SIGNATURE'] ?? '';
        $razorpayLib = new \App\Libraries\RazorpayLib();

        // Log the webhook data for debugging
        log_message('info', 'Razorpay Webhook: ' . json_encode($webhook));

        if (!$webhook || !isset($webhook['event'])) {
            return $this->response->setStatusCode(400)->setJSON(['status' => 'error', 'message' => 'Invalid webhook data']);
        }

        // Handle different webhook events
        switch ($webhook['event']) {
            case 'payment.authorized':
                // Payment has been authorized
                $paymentId = $webhook['payload']['payment']['entity']['id'] ?? null;

                if ($paymentId) {
                    // Find the order with this payment ID
                    $orderModel = new \App\Models\OrderModel();
                    $order = $orderModel->where('transaction_id', $paymentId)->first();

                    if ($order) {
                        // Update order status to completed
                        $orderModel->update($order['id'], ['payment_status' => 'completed']);
                        log_message('info', 'Order #' . $order['id'] . ' marked as completed');
                    }
                }
                break;

            case 'payment.failed':
                // Payment has failed
                $paymentId = $webhook['payload']['payment']['entity']['id'] ?? null;

                if ($paymentId) {
                    // Find the order with this payment ID
                    $orderModel = new \App\Models\OrderModel();
                    $order = $orderModel->where('transaction_id', $paymentId)->first();

                    if ($order) {
                        // Update order status to failed
                        $orderModel->update($order['id'], ['payment_status' => 'failed']);
                        log_message('info', 'Order #' . $order['id'] . ' marked as failed');
                    }
                }
                break;
        }

        return $this->response->setJSON(['status' => 'success']);
    }
}
