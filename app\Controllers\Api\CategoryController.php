<?php

namespace App\Controllers\Api;

use App\Controllers\BaseController;
use App\Models\CategoryModel;
use CodeIgniter\API\ResponseTrait;

class CategoryController extends BaseController
{
    use ResponseTrait;

    protected $categoryModel;

    public function __construct()
    {
        $this->categoryModel = new CategoryModel();
    }

    /**
     * Get all categories
     *
     * @return \CodeIgniter\HTTP\Response
     */
    public function index()
    {
        try {
            $categories = $this->categoryModel->findAll();

            // Format categories data
            $formattedCategories = [];
            foreach ($categories as $category) {
                $formattedCategories[] = [
                    'id' => $category['id'],
                    'name' => $category['name'],
                    'description' => $category['description'],
                    'preview_image' => $category['preview_image'] ? (strpos($category['preview_image'], 'uploads/categories/') === 0 ? base_url($category['preview_image']) : base_url('uploads/categories/' . $category['preview_image'])) : null
                ];
            }

            return $this->respond([
                'status' => true,
                'categories' => $formattedCategories
            ]);
        } catch (\Exception $e) {
            log_message('error', 'Get categories error: ' . $e->getMessage());
            return $this->fail('Failed to retrieve categories: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Get category details
     *
     * @param int $id
     * @return \CodeIgniter\HTTP\Response
     */
    public function show($id)
    {
        try {
            $category = $this->categoryModel->find($id);

            if (!$category) {
                return $this->failNotFound('Category not found.');
            }

            // Format category data
            $formattedCategory = [
                'id' => $category['id'],
                'name' => $category['name'],
                'description' => $category['description'],
                'preview_image' => $category['preview_image'] ? (strpos($category['preview_image'], 'uploads/categories/') === 0 ? base_url($category['preview_image']) : base_url('uploads/categories/' . $category['preview_image'])) : null
            ];

            return $this->respond([
                'status' => true,
                'category' => $formattedCategory
            ]);
        } catch (\Exception $e) {
            log_message('error', 'Get category details error: ' . $e->getMessage());
            return $this->fail('Failed to retrieve category details: ' . $e->getMessage(), 500);
        }
    }
}
