<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Models\UserModel;
use App\Libraries\GoogleClient;

class Auth extends BaseController
{
    protected $userModel;
    protected $googleClient;

    public function __construct()
    {
        $this->userModel = new UserModel();
        $this->googleClient = new GoogleClient();
    }

    public function login()
    {
        // Check if already logged in
        if (session()->get('isLoggedIn') && session()->get('isAdmin')) {
            return redirect()->to('admin/dashboard');
        }

        $data = [
            'title' => 'Admin Login - Sample Paper Store',
            'googleLoginUrl' => $this->googleClient->getAdminLoginUrl()
        ];

        if ($this->request->getMethod() === 'POST') {
            $rules = [
                'email' => 'required|valid_email',
                'password' => 'required|min_length[6]'
            ];

            if ($this->validate($rules)) {
                $email = $this->request->getPost('email');
                $password = $this->request->getPost('password');

                $user = $this->userModel->where('email', $email)->first();

                if ($user && password_verify($password, $user['password'])) {
                    // Check if user is active and is an admin
                    if ($user['status'] !== 'active') {
                        return redirect()->back()->withInput()->with('error', 'Your account is not active. Please contact support.');
                    }

                    if ($user['is_admin'] !== 'admin') {
                        return redirect()->back()->withInput()->with('error', 'You do not have admin privileges.');
                    }

                    // Set session data
                    $sessionData = [
                        'id' => $user['id'],
                        'name' => $user['name'],
                        'email' => $user['email'],
                        'isAdmin' => true,
                        'isLoggedIn' => true,
                        'profile_image' => $user['profile_image'] ?? null
                    ];

                    session()->set($sessionData);

                    return redirect()->to('admin/dashboard');
                } else {
                    return redirect()->back()->withInput()->with('error', 'Invalid email or password');
                }
            } else {
                return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
            }
        }

        return view('admin/auth/login', $data);
    }

    public function googleCallback()
    {
        $code = $this->request->getGet('code');

        if (!$code) {
            return redirect()->to('admin/login')->with('error', 'Authorization failed');
        }

        try {
            // Get user info from Google
            $userInfo = $this->googleClient->getUserInfo($code, true);

            // Debug log the user info
            log_message('debug', 'Google admin user info: ' . json_encode((array)$userInfo));

            // Check if user exists with this Google ID
            $user = $this->userModel->getUserByGoogleId($userInfo->id);

            if (!$user) {
                // Check if user exists with this email
                $user = $this->userModel->getUserByEmail($userInfo->email);

                if ($user) {
                    // Update existing user with Google ID
                    $this->userModel->update($user['id'], [
                        'google_id' => $userInfo->id,
                        'oauth_provider' => 'google',
                        'profile_image' => $userInfo->picture ?? null,
                        'updated_at' => date('Y-m-d H:i:s')
                    ]);
                } else {
                    // Create new user
                    $userData = [
                        'name' => $userInfo->name,
                        'email' => $userInfo->email,
                        'password' => password_hash(random_bytes(16), PASSWORD_DEFAULT), // Random password
                        'is_admin' => 'user', // Default to regular user
                        'status' => 'active',
                        'google_id' => $userInfo->id,
                        'oauth_provider' => 'google',
                        'profile_image' => $userInfo->picture ?? null,
                        'created_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s')
                    ];

                    $this->userModel->insert($userData);
                    $user = $this->userModel->getUserByGoogleId($userInfo->id);
                }
            }

            // Check if user is an admin
            if ($user['is_admin'] !== 'admin') {
                return redirect()->to('admin/login')->with('error', 'You do not have admin privileges');
            }

            // Set session data
            $sessionData = [
                'id' => $user['id'],
                'name' => $user['name'],
                'email' => $user['email'],
                'isAdmin' => true,
                'isLoggedIn' => true,
                'profile_image' => $user['profile_image'] ?? null
            ];

            session()->set($sessionData);

            return redirect()->to('admin/dashboard');
        } catch (\Exception $e) {
            log_message('error', 'Google admin authentication error: ' . $e->getMessage());
            log_message('error', 'Stack trace: ' . $e->getTraceAsString());
            return redirect()->to('admin/login')->with('error', 'Authentication failed: ' . $e->getMessage());
        } catch (\Error $e) {
            log_message('error', 'Google admin authentication error: ' . $e->getMessage());
            log_message('error', 'Stack trace: ' . $e->getTraceAsString());
            return redirect()->to('admin/login')->with('error', 'Authentication error: ' . $e->getMessage());
        }
    }

    public function logout()
    {
        session()->destroy();
        return redirect()->to('admin/login');
    }
}
