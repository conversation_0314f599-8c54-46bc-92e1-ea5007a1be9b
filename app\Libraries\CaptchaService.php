<?php

namespace App\Libraries;

class CaptchaService
{
    /**
     * Generate a simple math CAPTCHA
     *
     * @return array
     */
    public function generateMathCaptcha()
    {
        $num1 = rand(1, 10);
        $num2 = rand(1, 10);
        $operation = rand(0, 1) ? '+' : '-';
        
        if ($operation == '+') {
            $answer = $num1 + $num2;
            $question = "$num1 + $num2 = ?";
        } else {
            // Ensure the result is positive
            if ($num1 < $num2) {
                $temp = $num1;
                $num1 = $num2;
                $num2 = $temp;
            }
            $answer = $num1 - $num2;
            $question = "$num1 - $num2 = ?";
        }
        
        // Store the answer in session
        session()->set('captcha_answer', $answer);
        
        return [
            'question' => $question,
            'answer' => $answer
        ];
    }
    
    /**
     * Verify the CAPTCHA answer
     *
     * @param int $userAnswer
     * @return bool
     */
    public function verifyCaptcha($userAnswer)
    {
        $correctAnswer = session()->get('captcha_answer');
        
        // Clear the session after verification
        session()->remove('captcha_answer');
        
        return $userAnswer == $correctAnswer;
    }
}
