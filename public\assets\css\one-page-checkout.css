/* One-Page Checkout Styles */
.one-page-checkout {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.checkout-section {
    padding: 30px;
    border-bottom: 1px solid #f0f0f0;
}

.checkout-section:last-child {
    border-bottom: none;
}

.checkout-section-header {
    display: flex;
    align-items: center;
    margin-bottom: 25px;
}

.checkout-section-number {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: #4361ee;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    margin-right: 15px;
    flex-shrink: 0;
}

.checkout-section-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0;
    color: #333;
}

/* Payment Methods */
.payment-methods {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.payment-method {
    display: flex;
    align-items: center;
    padding: 20px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.payment-method:hover {
    border-color: #4361ee;
    background-color: rgba(67, 97, 238, 0.03);
}

.payment-method.active {
    border-color: #4361ee;
    background-color: rgba(67, 97, 238, 0.05);
}

.payment-method-radio {
    margin-right: 15px;
    width: 20px;
    height: 20px;
    accent-color: #4361ee;
}

.payment-method-logo {
    height: 40px;
    margin-right: 15px;
    object-fit: contain;
}

.payment-method-info {
    flex-grow: 1;
}

.payment-method-title {
    font-weight: 600;
    margin-bottom: 5px;
    color: #333;
}

.payment-method-description {
    color: #6c757d;
    font-size: 0.9rem;
    margin: 0;
}

/* Order Summary */
.checkout-summary {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
    padding: 30px;
    position: sticky;
    top: 30px;
}

.checkout-summary-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 1px solid #f0f0f0;
    color: #333;
}

.checkout-summary-items {
    margin-bottom: 20px;
}

.checkout-summary-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
    font-size: 0.95rem;
}

.checkout-summary-item-title {
    color: #333;
    font-weight: 500;
    flex-grow: 1;
    padding-right: 10px;
}

.checkout-summary-item-price {
    color: #4361ee;
    font-weight: 600;
}

.checkout-summary-total {
    display: flex;
    justify-content: space-between;
    font-weight: 700;
    font-size: 1.1rem;
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #e0e0e0;
    color: #333;
}

.checkout-summary-total div:last-child {
    color: #4361ee;
}

/* Product Image Styling */
.product-image {
    width: 100px;
    height: 100px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Responsive Adjustments */
@media (max-width: 991.98px) {
    .checkout-summary {
        position: static;
        margin-top: 30px;
    }
}

@media (max-width: 767.98px) {
    .checkout-section {
        padding: 20px;
    }
    
    .payment-method {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .payment-method-logo {
        margin-bottom: 10px;
        margin-right: 0;
    }
    
    .payment-method-radio {
        align-self: flex-start;
    }
}
