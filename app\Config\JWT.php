<?php

namespace Config;

use CodeIgniter\Config\BaseConfig;

class JWT extends BaseConfig
{
    /**
     * JWT Secret Key
     *
     * This key is used to sign the JWT tokens.
     * It should be a long, random string.
     * Keep this key secret!
     *
     * @var string
     */
    public $secretKey = 'your-secret-key-for-jwt-tokens-change-this-in-production';

    /**
     * JWT Time to Live (in seconds)
     *
     * This sets how long a token is valid.
     * Default is 1 hour (3600 seconds)
     *
     * @var int
     */
    public $ttl = 3600;

    /**
     * JWT Issuer
     *
     * This is the issuer claim in the JWT.
     * Usually, this is your application name or URL.
     *
     * @var string
     */
    public $issuer = 'sample-paper-store';

    /**
     * Token Header Name
     *
     * This is the name of the header where the JWT token will be stored.
     *
     * @var string
     */
    public $tokenHeader = 'Authorization';

    /**
     * Token Header Prefix
     *
     * This is the prefix used before the token in the Authorization header.
     * Example: Authorization: Bearer <token>
     *
     * @var string
     */
    public $tokenHeaderPrefix = 'Bearer';
}
