<?php

namespace Config;

use CodeIgniter\Config\BaseConfig;

class Google extends BaseConfig
{
    /**
     * Google Client ID
     *
     * @var string
     */
    public $clientId = '350378693553-1l78i205hmluktgllklddch4jnn9fs66.apps.googleusercontent.com';

    /**
     * Google Client Secret
     *
     * @var string
     */
    public $clientSecret = '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';

    /**
     * Google Redirect URI for user login
     *
     * @var string
     */
    public $redirectUri = 'http://localhost/sample-paper/auth/google/callback';

    /**
     * Google Redirect URI for admin login
     *
     * @var string
     */
    public $adminRedirectUri = 'http://localhost/sample-paper/admin/google/callback';

    /**
     * Google API Scopes
     *
     * @var array
     */
    public $scopes = [
        'email',
        'profile',
        'openid'
    ];
}
