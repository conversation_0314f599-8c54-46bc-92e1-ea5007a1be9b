/**
 * Sample Paper Store Premium Theme
 * Custom JavaScript
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Initialize hero slider
    initHeroSlider();

    // Initialize paper cards hover effects
    initPaperCards();

    // Initialize payment method selection
    initPaymentMethods();

    // Initialize quantity controls
    initQuantityControls();

    // Initialize mobile menu
    initMobileMenu();

    // Initialize AOS animations
    AOS.init({
        duration: 800,
        easing: 'ease-in-out',
        once: true
    });

    // Add to cart animation
    const addToCartButtons = document.querySelectorAll('.add-to-cart');
    addToCartButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();

            // Show success message
            const originalText = this.innerHTML;
            this.innerHTML = '<i class="fas fa-check"></i> Added';
            this.classList.remove('btn-primary');
            this.classList.add('btn-success');

            // Reset button after 2 seconds
            setTimeout(() => {
                this.innerHTML = originalText;
                this.classList.remove('btn-success');
                this.classList.add('btn-primary');
            }, 2000);

            // Submit the form
            this.closest('form').submit();
        });
    });

    // Preview paper functionality
    const previewButtons = document.querySelectorAll('.preview-paper');
    previewButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const paperId = this.getAttribute('data-paper-id');
            const previewUrl = this.getAttribute('data-preview-url');

            // Show preview modal
            const previewModal = new bootstrap.Modal(document.getElementById('previewModal'));
            document.getElementById('previewFrame').src = previewUrl;
            previewModal.show();
        });
    });

    // Search functionality
    const searchForm = document.getElementById('searchForm');
    if (searchForm) {
        searchForm.addEventListener('submit', function(e) {
            const searchInput = document.getElementById('searchInput');
            if (searchInput.value.trim() === '') {
                e.preventDefault();
                searchInput.classList.add('is-invalid');
            } else {
                searchInput.classList.remove('is-invalid');
            }
        });
    }

    // Filter functionality
    const filterForm = document.getElementById('filterForm');
    if (filterForm) {
        const filterInputs = filterForm.querySelectorAll('input, select');
        filterInputs.forEach(input => {
            input.addEventListener('change', function() {
                filterForm.submit();
            });
        });
    }
});

/**
 * Initialize paper cards hover effects
 */
function initPaperCards() {
    const paperCards = document.querySelectorAll('.paper-card');

    paperCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.classList.add('hover');
        });

        card.addEventListener('mouseleave', function() {
            this.classList.remove('hover');
        });
    });
}

/**
 * Initialize payment method selection
 */
function initPaymentMethods() {
    const paymentMethods = document.querySelectorAll('.payment-method');

    if (paymentMethods.length > 0) {
        paymentMethods.forEach(method => {
            method.addEventListener('click', function() {
                // Remove active class from all methods
                paymentMethods.forEach(m => m.classList.remove('active'));

                // Add active class to clicked method
                this.classList.add('active');

                // Select the radio button
                const radio = this.querySelector('input[type="radio"]');
                if (radio) {
                    radio.checked = true;
                }
            });
        });
    }
}

/**
 * Initialize quantity controls
 */
function initQuantityControls() {
    const quantityControls = document.querySelectorAll('.quantity-control');

    if (quantityControls.length > 0) {
        quantityControls.forEach(control => {
            const decreaseBtn = control.querySelector('.decrease');
            const increaseBtn = control.querySelector('.increase');
            const input = control.querySelector('input');

            if (decreaseBtn && increaseBtn && input) {
                decreaseBtn.addEventListener('click', function() {
                    let value = parseInt(input.value);
                    if (value > 1) {
                        input.value = value - 1;
                        // Trigger change event
                        const event = new Event('change');
                        input.dispatchEvent(event);
                    }
                });

                increaseBtn.addEventListener('click', function() {
                    let value = parseInt(input.value);
                    input.value = value + 1;
                    // Trigger change event
                    const event = new Event('change');
                    input.dispatchEvent(event);
                });
            }
        });
    }
}

/**
 * Initialize mobile menu
 */
function initMobileMenu() {
    const navbar = document.querySelector('.navbar');
    const navbarToggler = document.querySelector('.navbar-toggler');

    if (navbar && navbarToggler) {
        navbarToggler.addEventListener('click', function() {
            navbar.classList.toggle('mobile-open');
        });
    }

    // Add scroll event for navbar
    window.addEventListener('scroll', function() {
        if (window.scrollY > 50) {
            navbar.classList.add('navbar-scrolled');
        } else {
            navbar.classList.remove('navbar-scrolled');
        }
    });
}

/**
 * Initialize hero slider
 */
function initHeroSlider() {
    const heroSlider = document.querySelector('.hero-slider');

    if (heroSlider) {
        new Swiper(heroSlider, {
            slidesPerView: 1,
            spaceBetween: 0,
            loop: true,
            autoplay: {
                delay: 5000,
                disableOnInteraction: false,
            },
            effect: 'fade',
            fadeEffect: {
                crossFade: true
            },
            speed: 1000,
            pagination: {
                el: '.swiper-pagination',
                clickable: true,
            },
            navigation: {
                nextEl: '.swiper-button-next',
                prevEl: '.swiper-button-prev',
            },
        });
    }

    // Initialize testimonial slider
    const testimonialSlider = document.querySelector('.testimonial-slider');

    if (testimonialSlider) {
        new Swiper(testimonialSlider, {
            slidesPerView: 1,
            spaceBetween: 30,
            loop: true,
            autoplay: {
                delay: 6000,
                disableOnInteraction: false,
            },
            speed: 800,
            pagination: {
                el: '.testimonial-slider .swiper-pagination',
                clickable: true,
            },
            navigation: {
                nextEl: '.testimonial-button-next',
                prevEl: '.testimonial-button-prev',
            },
            breakpoints: {
                768: {
                    slidesPerView: 2,
                },
                1024: {
                    slidesPerView: 3,
                },
            }
        });
    }
}

/**
 * Handle Razorpay payment
 * @param {Object} options - Razorpay options
 */
function handleRazorpayPayment(options) {
    try {
        const rzp = new Razorpay(options);

        rzp.on('payment.failed', function(response) {
            console.error('Payment failed:', response.error);
            const errorElement = document.getElementById('payment-error');
            if (errorElement) {
                errorElement.innerHTML = '<p class="mb-0"><i class="fas fa-exclamation-circle me-2"></i>Payment failed: ' + response.error.description + '</p>';
                errorElement.style.display = 'block';
            }
        });

        rzp.open();
    } catch (error) {
        console.error('Razorpay error:', error);
        const errorElement = document.getElementById('payment-error');
        if (errorElement) {
            errorElement.innerHTML = '<p class="mb-0"><i class="fas fa-exclamation-circle me-2"></i>Error initializing payment: ' + error.message + '</p>';
            errorElement.style.display = 'block';
        }
    }
}