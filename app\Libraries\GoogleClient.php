<?php

namespace App\Libraries;

use Config\Google as GoogleConfig;
use CodeIgniter\Config\Services;

class GoogleClient
{
    protected $client;
    protected $config;

    public function __construct()
    {
        $this->config = new GoogleConfig();
        $this->client = new \Google\Client();

        $this->client->setClientId($this->config->clientId);
        $this->client->setClientSecret($this->config->clientSecret);
        $this->client->setScopes($this->config->scopes);
        $this->client->setAccessType('offline');
        $this->client->setPrompt('select_account consent');
    }

    /**
     * Get Google login URL for users
     */
    public function getUserLoginUrl()
    {
        // Ensure we have an absolute URL by using base_url()
        $redirectUri = $this->config->redirectUri;
        if (!filter_var($redirectUri, FILTER_VALIDATE_URL)) {
            $redirectUri = base_url($redirectUri);
        }
        $this->client->setRedirectUri($redirectUri);
        return $this->client->createAuthUrl();
    }

    /**
     * Get Google login URL for admins
     */
    public function getAdminLoginUrl()
    {
        // Ensure we have an absolute URL by using base_url()
        $redirectUri = $this->config->adminRedirectUri;
        if (!filter_var($redirectUri, FILTER_VALIDATE_URL)) {
            $redirectUri = base_url($redirectUri);
        }
        $this->client->setRedirectUri($redirectUri);
        return $this->client->createAuthUrl();
    }

    /**
     * Get user profile information from Google
     */
    public function getUserInfo($code, $isAdmin = false)
    {
        if ($isAdmin) {
            $redirectUri = $this->config->adminRedirectUri;
            if (!filter_var($redirectUri, FILTER_VALIDATE_URL)) {
                $redirectUri = base_url($redirectUri);
            }
            $this->client->setRedirectUri($redirectUri);
        } else {
            $redirectUri = $this->config->redirectUri;
            if (!filter_var($redirectUri, FILTER_VALIDATE_URL)) {
                $redirectUri = base_url($redirectUri);
            }
            $this->client->setRedirectUri($redirectUri);
        }

        // Exchange authorization code for access token
        $token = $this->client->fetchAccessTokenWithAuthCode($code);
        $this->client->setAccessToken($token);

        // Get user profile
        $service = new \Google\Service\Oauth2($this->client);
        return $service->userinfo->get();
    }
}
