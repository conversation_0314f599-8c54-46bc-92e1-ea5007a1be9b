<?= $this->extend('templates/admin_layout') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800"><?= $title ?></h1>
        <a href="<?= base_url('admin/users') ?>" class="d-none d-sm-inline-block btn btn-sm btn-secondary shadow-sm">
            <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to Users
        </a>
    </div>

    <?php if (session()->getFlashdata('success')): ?>
        <div class="alert alert-success">
            <?= session()->getFlashdata('success') ?>
        </div>
    <?php endif; ?>

    <?php if (session()->getFlashdata('error')): ?>
        <div class="alert alert-danger">
            <?= session()->getFlashdata('error') ?>
        </div>
    <?php endif; ?>

    <!-- User Details Card -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 font-weight-bold text-primary">User Information</h6>
            <div class="dropdown no-arrow">
                <a class="dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
                </a>
                <div class="dropdown-menu dropdown-menu-right shadow animated--fade-in" aria-labelledby="dropdownMenuLink">
                    <a class="dropdown-item" href="<?= base_url('admin/users/edit/' . $user['id']) ?>">
                        <i class="fas fa-edit fa-sm fa-fw mr-2 text-gray-400"></i>
                        Edit User
                    </a>
                    <?php if ($user['status'] === 'active'): ?>
                        <a class="dropdown-item" href="<?= base_url('admin/users/ban/' . $user['id']) ?>" onclick="return confirm('Are you sure you want to ban this user?')">
                            <i class="fas fa-ban fa-sm fa-fw mr-2 text-gray-400"></i>
                            Ban User
                        </a>
                    <?php else: ?>
                        <a class="dropdown-item" href="<?= base_url('admin/users/unban/' . $user['id']) ?>" onclick="return confirm('Are you sure you want to unban this user?')">
                            <i class="fas fa-check fa-sm fa-fw mr-2 text-gray-400"></i>
                            Unban User
                        </a>
                    <?php endif; ?>
                    <div class="dropdown-divider"></div>
                    <a class="dropdown-item text-danger" href="<?= base_url('admin/users/delete/' . $user['id']) ?>" onclick="return confirm('Are you sure you want to delete this user?')">
                        <i class="fas fa-trash fa-sm fa-fw mr-2 text-gray-400"></i>
                        Delete User
                    </a>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-3 text-center mb-4">
                    <?php if (!empty($user['profile_image'])): ?>
                        <img src="<?= base_url('uploads/profiles/' . $user['profile_image']) ?>" alt="Profile Image" class="img-profile rounded-circle" style="width: 150px; height: 150px;">
                    <?php else: ?>
                        <img src="<?= base_url('assets/img/undraw_profile.svg') ?>" alt="Default Profile" class="img-profile rounded-circle" style="width: 150px; height: 150px;">
                    <?php endif; ?>
                </div>
                <div class="col-md-9">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <tr>
                                <th width="200">User ID</th>
                                <td><?= $user['id'] ?></td>
                            </tr>
                            <tr>
                                <th>Name</th>
                                <td><?= esc($user['name']) ?></td>
                            </tr>
                            <tr>
                                <th>Email</th>
                                <td><?= esc($user['email']) ?></td>
                            </tr>
                            <tr>
                                <th>Role</th>
                                <td>
                                    <span class="badge badge-<?= $user['is_admin'] === 'admin' ? 'primary' : 'secondary' ?>">
                                        <?= ucfirst($user['is_admin']) ?>
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <th>Status</th>
                                <td>
                                    <span class="badge badge-<?= $user['status'] === 'active' ? 'success' : 'danger' ?>">
                                        <?= ucfirst($user['status']) ?>
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <th>Created At</th>
                                <td><?= date('Y-m-d H:i:s', strtotime($user['created_at'])) ?></td>
                            </tr>
                            <tr>
                                <th>Last Updated</th>
                                <td><?= date('Y-m-d H:i:s', strtotime($user['updated_at'])) ?></td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>