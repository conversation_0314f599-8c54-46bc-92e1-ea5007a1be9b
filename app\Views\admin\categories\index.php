<?= $this->extend('templates/admin_layout') ?>

<?= $this->section('content') ?>
<div class="container-fluid px-4">
    <h1 class="mt-4"><?= $title ?></h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="<?=base_url('/admin/dashboard')?>">Dashboard</a></li>
        <li class="breadcrumb-item active">Categories</li>
    </ol>

    <?php if (session()->getFlashdata('success')) : ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?= session()->getFlashdata('success') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-table me-1"></i>
            Categories List
            <a href="<?=base_url('/admin/categories/create')?>" class="btn btn-primary btn-sm float-end">Add New Category</a>
        </div>
        <div class="card-body">
            <table id="categoriesTable" class="table table-striped">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Name</th>
                        <th>Image</th>
                        <th>Description</th>
                        <th>Created At</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($categories as $category) : ?>
                        <tr>
                            <td><?= $category['id'] ?></td>
                            <td><?= esc($category['name']) ?></td>
                            <td>
                                <?php if ($category['preview_image']) : ?>
                                    <img src="<?=base_url( $category['preview_image'] )?>" alt="<?= esc($category['name']) ?>" style="max-width: 50px;">
                                <?php endif; ?>
                            </td>
                            <td><?= esc($category['description']) ?></td>
                            <td><?= date('Y-m-d H:i:s', strtotime($category['created_at'])) ?></td>
                            <td>
                                <a href="<?=base_url('/admin/categories/edit/'.$category['id'] )?>" class="btn btn-primary btn-sm">Edit</a>
                                <button type="button" class="btn btn-danger btn-sm" data-bs-toggle="modal" data-bs-target="#deleteModal<?= $category['id'] ?>">Delete</button>

                                <!-- Delete Modal -->
                                <div class="modal fade" id="deleteModal<?= $category['id'] ?>" tabindex="-1" aria-labelledby="deleteModalLabel<?= $category['id'] ?>" aria-hidden="true">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title" id="deleteModalLabel<?= $category['id'] ?>">Confirm Delete</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                            </div>
                                            <div class="modal-body">
                                                Are you sure you want to delete the category "<?= esc($category['name']) ?>"?
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                <a href="<?=base_url('admin/categories/delete/'. $category['id']) ?>" class="btn btn-danger">Delete</a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<?= $this->section('scripts') ?>
<script>
    $(document).ready(function() {
        $('#categoriesTable').DataTable();
    });
</script>
<?= $this->endSection() ?>

<?= $this->endSection() ?>