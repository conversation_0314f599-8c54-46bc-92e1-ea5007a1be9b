<?= $this->extend('templates/admin_layout') ?>

<?= $this->section('content') ?>
<div class="container-fluid px-4">
    <h1 class="mt-4"><?= $title ?></h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="<?=base_url('/admin/dashboard')?>">Dashboard</a></li>
        <li class="breadcrumb-item active">Categories</li>
    </ol>

    <?php if (session()->getFlashdata('success')) : ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?= session()->getFlashdata('success') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <div class="card mb-4">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <div>
                    <i class="fas fa-table me-1"></i>
                    Categories List
                </div>
                <a href="<?=base_url('/admin/categories/create')?>" class="btn btn-primary btn-sm">Add New Category</a>
            </div>

            <!-- Search and Filter Controls -->
            <div class="row g-3">
                <div class="col-md-6">
                    <form action="<?= base_url('admin/categories') ?>" method="get" class="d-flex">
                        <input type="text" class="form-control form-control-sm"
                               name="search" placeholder="Search categories..."
                               value="<?= esc($search ?? '') ?>">
                        <button type="submit" class="btn btn-outline-primary btn-sm ms-2">
                            <i class="fas fa-search"></i>
                        </button>
                        <?php if (!empty($search)): ?>
                            <a href="<?= base_url('admin/categories') ?>" class="btn btn-outline-secondary btn-sm ms-1">
                                <i class="fas fa-times"></i>
                            </a>
                        <?php endif; ?>
                    </form>
                </div>
                <div class="col-md-6">
                    <div class="d-flex justify-content-end align-items-center">
                        <label class="form-label me-2 mb-0 small">Show:</label>
                        <select class="form-select form-select-sm" style="width: auto;" onchange="changePerPage(this.value)">
                            <option value="5" <?= $perPage == 5 ? 'selected' : '' ?>>5</option>
                            <option value="10" <?= $perPage == 10 ? 'selected' : '' ?>>10</option>
                            <option value="25" <?= $perPage == 25 ? 'selected' : '' ?>>25</option>
                            <option value="50" <?= $perPage == 50 ? 'selected' : '' ?>>50</option>
                        </select>
                        <span class="small text-muted ms-2">entries</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-body">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Name</th>
                        <th>Image</th>
                        <th>Description</th>
                        <th>Created At</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (!empty($categories)): ?>
                        <?php foreach ($categories as $index => $category) : ?>
                        <tr>
                            <td><?= ($currentPage - 1) * $perPage + $index + 1 ?></td>
                            <td><?= esc($category['name']) ?></td>
                            <td>
                                <?php if ($category['preview_image']) : ?>
                                    <img src="<?=base_url( $category['preview_image'] )?>" alt="<?= esc($category['name']) ?>" style="max-width: 50px;">
                                <?php endif; ?>
                            </td>
                            <td><?= esc($category['description']) ?></td>
                            <td><?= date('Y-m-d H:i:s', strtotime($category['created_at'])) ?></td>
                            <td>
                                <a href="<?=base_url('/admin/categories/edit/'.$category['id'] )?>" class="btn btn-primary btn-sm">Edit</a>
                                <button type="button" class="btn btn-danger btn-sm" data-bs-toggle="modal" data-bs-target="#deleteModal<?= $category['id'] ?>">Delete</button>

                                <!-- Delete Modal -->
                                <div class="modal fade" id="deleteModal<?= $category['id'] ?>" tabindex="-1" aria-labelledby="deleteModalLabel<?= $category['id'] ?>" aria-hidden="true">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title" id="deleteModalLabel<?= $category['id'] ?>">Confirm Delete</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                            </div>
                                            <div class="modal-body">
                                                Are you sure you want to delete the category "<?= esc($category['name']) ?>"?
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                <a href="<?=base_url('admin/categories/delete/'. $category['id']) ?>" class="btn btn-danger">Delete</a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="6" class="text-center py-4">
                                <div class="text-muted">
                                    <i class="fas fa-folder-open fa-3x mb-3"></i>
                                    <p>No categories found</p>
                                </div>
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>

            <!-- Pagination -->
            <?php if (isset($pager) && $pager->getPageCount() > 1): ?>
            <div class="d-flex justify-content-between align-items-center mt-3">
                <div class="text-muted small">
                    Showing <?= ($currentPage - 1) * $perPage + 1 ?> to
                    <?= min($currentPage * $perPage, $pager->getTotal()) ?> of
                    <?= $pager->getTotal() ?> entries
                    <?php if (!empty($search)): ?>
                        (filtered from total entries)
                    <?php endif; ?>
                </div>
                <div>
                    <?= $pager->links('default', 'bootstrap_pagination') ?>
                </div>
            </div>
            <?php elseif (isset($pager)): ?>
            <div class="text-muted small mt-3">
                Total: <?= $pager->getTotal() ?> entries
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
function changePerPage(perPage) {
    const url = new URL(window.location);
    url.searchParams.set('per_page', perPage);
    url.searchParams.delete('page'); // Reset to first page
    window.location.href = url.toString();
}
</script>

<style>
.card-header {
    background-color: #fff;
    border-bottom: 1px solid #e3e6f0;
}

.table-responsive {
    border-radius: 0.375rem;
    overflow: hidden;
}

.table th {
    font-weight: 600;
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
}

.table td {
    vertical-align: middle;
}

.pagination .page-link {
    color: #2C3E50;
    background-color: #fff;
    border: 1px solid #dee2e6;
}

.pagination .page-item.active .page-link {
    background-color: #2C3E50;
    border-color: #2C3E50;
    color: #fff;
}
</style>

<?= $this->endSection() ?>