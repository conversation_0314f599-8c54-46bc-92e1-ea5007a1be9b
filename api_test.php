<?php
/**
 * Simple API Test Script
 * 
 * This script tests the JWT authentication API endpoints.
 * Run this script from the command line: php api_test.php
 */

// Base URL of your application
$baseUrl = 'http://localhost/sample-paper/';

// Test user credentials
$testUser = [
    'email' => '<EMAIL>',
    'password' => 'password123'
];

// Colors for console output
$green = "\033[32m";
$red = "\033[31m";
$reset = "\033[0m";

echo "Starting API Tests...\n\n";

// Test 1: Register a new user
echo "Test 1: Register a new user\n";
$registerData = [
    'name' => 'Test User',
    'email' => $testUser['email'],
    'password' => $testUser['password'],
    'confirm_password' => $testUser['password']
];

$registerResponse = makeRequest('api/auth/register', 'POST', $registerData);
printResponse($registerResponse);

// If registration fails because user already exists, try logging in
if (isset($registerResponse['status']) && $registerResponse['status'] === false) {
    echo "User might already exist. Trying to login...\n";
}

// Test 2: Login
echo "\nTest 2: Login\n";
$loginResponse = makeRequest('api/auth/login', 'POST', $testUser);
printResponse($loginResponse);

// If login successful, get the token for further tests
$token = isset($loginResponse['token']) ? $loginResponse['token'] : null;

if (!$token) {
    die($red . "Login failed. Cannot continue tests without authentication token.\n" . $reset);
}

// Test 3: Get user profile
echo "\nTest 3: Get user profile\n";
$profileResponse = makeRequest('api/auth/profile', 'GET', null, $token);
printResponse($profileResponse);

// Test 4: Get all papers
echo "\nTest 4: Get all papers\n";
$papersResponse = makeRequest('api/papers', 'GET');
printResponse($papersResponse);

// Test 5: Get all categories
echo "\nTest 5: Get all categories\n";
$categoriesResponse = makeRequest('api/categories', 'GET');
printResponse($categoriesResponse);

// Test 6: Refresh token
echo "\nTest 6: Refresh token\n";
$refreshResponse = makeRequest('api/auth/refresh-token', 'POST', null, $token);
printResponse($refreshResponse);

// Update token if refresh was successful
if (isset($refreshResponse['token'])) {
    $token = $refreshResponse['token'];
    echo "Token updated successfully.\n";
}

echo "\nAPI Tests Completed.\n";

/**
 * Make an HTTP request to the API
 * 
 * @param string $endpoint API endpoint
 * @param string $method HTTP method (GET, POST, etc.)
 * @param array|null $data Request data
 * @param string|null $token JWT token for authentication
 * @return array Response data
 */
function makeRequest($endpoint, $method = 'GET', $data = null, $token = null)
{
    global $baseUrl;
    
    $url = $baseUrl . $endpoint;
    
    $ch = curl_init($url);
    
    $headers = ['Content-Type: application/json'];
    
    if ($token) {
        $headers[] = 'Authorization: Bearer ' . $token;
    }
    
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    
    if ($method === 'POST') {
        curl_setopt($ch, CURLOPT_POST, true);
        if ($data) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        }
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    
    curl_close($ch);
    
    $responseData = json_decode($response, true);
    
    return [
        'http_code' => $httpCode,
        'response' => $responseData
    ];
}

/**
 * Print API response in a readable format
 * 
 * @param array $response Response data
 */
function printResponse($response)
{
    global $green, $red, $reset;
    
    $httpCode = $response['http_code'];
    $responseData = $response['response'];
    
    echo "HTTP Status: ";
    if ($httpCode >= 200 && $httpCode < 300) {
        echo $green . $httpCode . $reset . "\n";
    } else {
        echo $red . $httpCode . $reset . "\n";
    }
    
    echo "Response: ";
    if ($responseData) {
        echo json_encode($responseData, JSON_PRETTY_PRINT) . "\n";
    } else {
        echo $red . "No response or invalid JSON" . $reset . "\n";
    }
}
